<template>
  <div>
    <BsCardVue :title="title">
      <template #actions>
        <div class="flex justify-center items-center">
          <Button
            v-if="fileShowList.length > 0"
            class="h-[28px] mr-2"
            variant="destructive"
            size="sm"
            @click="resetUpload"
          >
            <span>清除数据</span>
          </Button>
          <!-- <Button class="h-[28px]" @click="onSubmitData">
            <Loader2 v-if="isLoading" class="w-4 h-4 animate-spin" />
            <Upload v-else class="w-4 h-4" />
            <span>上传数据集</span>
          </Button> -->
        </div>
      </template>
      <div>
        <FileDropUpload
          ref="fileDropUpload"
          :progress-control="true"
          :accept-types="acceptTypes"
          :max-size="maxSize"
          @file-selected="(file) => handleFile(file)"
        />
      </div>
      <div class="grow mt-4">
        <div class="border rounded-lg">
          <h3 class="flex justify-between items-center text-lg font-bold line-clamp-1 border-b p-4">
            <span>数据集合</span>
            <Badge variant="secondary">已上传{{ fileShowList.length }} 个数据集</Badge>
            <!-- <div>
          <div class="flex items-center space-x-2">
            <Checkbox id="selectAll" v-model="selectAll" @update:model-value="onSelectAll" />
            <label
              for="selectAll"
              class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              全部
            </label>
          </div>
        </div> -->
          </h3>
          <div class="p-4">
            <div
              v-for="(item, index) in fileShowList"
              :key="index"
              class="border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all bg-white dark:bg-gray-800 mb-2"
            >
              <div class="p-4 rounded-lg border">
                <div class="flex justify-end items-center mb-4">
                  <div v-if="item.progress < 100" class="w-full flex justify-between items-center">
                    <div class="w-[166px] text-sm">上传中:{{ item.progress }}%</div>
                    <Progress v-model="item.progress" :min="0" :max="100" />
                  </div>
                  <div
                    v-if="item.progress >= 100"
                    class="w-full flex justify-between items-center text-green-500"
                  >
                    <span class="text-sm text-green-500">上传完成</span>
                    <CircleCheckBig class="w-4 h-4" />
                  </div>
                </div>
                <div class="flex justify-between items-center">
                  <div class="flex justify-start items-center">
                    <div>
                      <img
                        class="w-7 h-8"
                        :src="item.type && item.type === '.csv' ? FileCsvSvg : FileAllSvg"
                        alt="#"
                      />
                    </div>
                    <span
                      class="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1 ml-2"
                    >
                      {{ item.name }}
                    </span>
                  </div>
                  <div class="flex justify-start items-center">
                    <!-- <div class="flex items-center check-btn">
                  <Checkbox v-model="item.check" @update:model-value="onSelectItem" />
                </div> -->
                    <div class="cursor-pointer bg-red-500 p-1 rounded-sm" @click="onDelete(index)">
                      <Trash class="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <!-- <div v-if="item.isUpload">
                <span v-if="item.progress < 100">{{ item.progress }}</span>
                <Loader2 v-if="item.progress < 100" class="w-4 h-4 animate-spin" />
                <CircleCheckBig v-if="item.progress >= 100" class="w-4 h-4" />
              </div> -->
                </div>
              </div>
              <!--  -->
              <!--  -->
            </div>
          </div>
        </div>
      </div>
    </BsCardVue>
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>您确定要删除此数据吗？</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction class="bg-red-500 hover:bg-red-600" @click="confirmDelete">
            确认删除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>
<script setup lang="ts">
import FileAllSvg from '@renderer/assets/svg/fileAll.svg'
import FileCsvSvg from '@renderer/assets/svg/fileCsv.svg'
import { FileDropUpload } from '@renderer/components'
import { getNodeParams, saveNodeParams } from '@renderer/utils/nodeUtils'
import { CircleCheckBig, Trash } from 'lucide-vue-next'
import { nanoid } from 'nanoid'
import { onMounted, onUnmounted, ref, Ref } from 'vue'
import BsCardVue from '../components/BsCard.vue'
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})
const title: string = '数据集导入'
const maxSize: number = 1024
const acceptTypes: string[] = ['.csv', '.pkl']
const fileShowList: Ref<any> = ref([])
const handleFile = (file: File) => {
  if (fileDropUpload.value) {
    fileDropUpload.value.resetUploading()
  }
  uploadCsvFile(file)
}
// const selectAll: Ref<boolean> = ref(false)
// const onSelectAll = () => {
//   let isUploadeNum = 0
//   fileShowList.value.map((m) => {
//     if (!m.isUpload) {
//       return (m.check = selectAll.value)
//     } else {
//       isUploadeNum++
//       return m
//     }
//   })
//   if (isUploadeNum === fileShowList.value.length) {
//     selectAll.value = true
//   }
//   inputChangeEnd()
// }
// const onSelectItem = () => {
//   selectAllData()
//   inputChangeEnd()
// }
// const selectAllData = () => {
//   selectAll.value = fileShowList.value.every((item) => item.check)
// }
let delIndex: number = -1
const onDelete = (index: number) => {
  delIndex = index
  isDeleteDialogOpen.value = true
}
const isDeleteDialogOpen: Ref<boolean> = ref(false)
const confirmDelete = () => {
  fileShowList.value.splice(delIndex, 1)
  isDeleteDialogOpen.value = false
  if (resultList.length > 0) {
    resultList.splice(delIndex, 1)
  }
  inputChangeEnd()
}
const fileDropUpload: any = ref(null)
onMounted(() => {
  loadInitData()
})
const loadInitData = () => {
  const currentParams = getNodeParams(props.nodeData)
  fileShowList.value = JSON.parse(currentParams.showList || '[]')
  resultList = JSON.parse(currentParams.input_datas || '[]')
  // 初始化传递参数
  saveNextParams({
    input_datas: JSON.stringify(resultList),
    showList: JSON.stringify(fileShowList.value),
  })
  // selectAllData()
}
const uploadCsvFile = async (file) => {
  const uploadId = nanoid()
  const metadata: any = {}
  metadata.filePath = file.path
  metadata.uploadId = uploadId
  metadata.userId = '0'
  metadata.dataType = 'study'
  const fileName = file.name
  const fileType = file.name.lastIndexOf('.') > -1 ? '.' + file.name.split('.').pop() : ''
  fileShowList.value.push({
    name: fileName, // 文件名称
    path: '',
    type: fileType, // 文件类型
    progress: 10,
    // check: true, // 是否选中
    // isSelect: '0', // 是否选择
    // cycleRange: [-1, -1],
  })
  const index = fileShowList.value.length - 1
  await window.grpcApi.uploadCsvFile(metadata, (data: any) => {
    handleUploadResult(data, {
      name: fileName,
      type: fileType,
      index,
    })
  })
}
let resultList: any = []
// let pro = 0
const handleUploadResult = (data: any, fileInfo: any) => {
  const pro = Math.round(data.progress * 100) / 100 / (100 / 50)
  if (pro > fileShowList.value[fileInfo.index].progress) {
    fileShowList.value[fileInfo.index].progress = pro
  }
  if (data.status === 'finished') {
    if (data.step === 'upload') {
      if (data?.data?.stored_filepath) {
        const path = data.data.stored_filepath
        const index = fileInfo.index
        fileShowList.value[index].path = path
        fileShowList.value[index].progress = 100
        inputChangeEnd()
      }
    }
  }
}
const inputChangeEnd = () => {
  resultList = []
  fileShowList.value.forEach((item) => {
    resultList.push({
      file_path: item.path,
      path: item.name,
      // start_cycle: item.cycleRange[0],
      // end_cycle: item.cycleRange[1],
    })
  })
  // selectAllData()
  saveNextParams({
    input_datas: JSON.stringify(resultList),
    showList: JSON.stringify(fileShowList.value),
  })
}
const saveNextParams = (params: any) => {
  saveNodeParams(props.nodeData, {
    ...params,
  })
}
const resetUpload = () => {
  fileShowList.value = []
  resultList = []
  saveNextParams({
    input_datas: JSON.stringify(resultList),
    showList: JSON.stringify(fileShowList.value),
  })
}
onUnmounted(() => {
  // console.log('---unmounted---')
})
</script>
<style scoped>
.del-container {
  cursor: pointer;
  position: relative;
}
.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  display: none;
}
.del-container:hover .delete-btn {
  display: block;
}
</style>
