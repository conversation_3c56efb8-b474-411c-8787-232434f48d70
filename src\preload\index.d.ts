import { ElectronAPI } from '@electron-toolkit/preload'
type GrpcCallback<T> = (res: T) => void
type AgentCallbck<T> = (type: string, data: T) => void

interface FileApi {
  saveFile: (options: any) => Promise<any>
  openFile: (options: any) => Promise<any>
  readFile: (
    filePath: string | { filePath: string; encoding?: string | null; isBinary?: boolean },
  ) => Promise<any>
  encryptAndSaveFile: (options: any) => Promise<any>
  openSaveFileDialog: (options: any) => Promise<any>
}

interface AppInfoApi {
  isMatt: boolean
  defaultWorkflowId: string
  getDefaultRoutePath: () => string
  getLocalIp: () => string
}

interface LoggerApi {
  error: (...args: any[]) => void
  warn: (...args: any[]) => void
  info: (...args: any[]) => void
  debug: (...args: any[]) => void
  getLogPath: () => string
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: unknown
    electronAPI: FileApi
    appInfo: AppInfoApi
    logger: LoggerApi
    grpcApi: {
      call: <T = unknown>(
        apiName: string,
        params: unknown,
        callBack?: GrpcCallback<T>,
      ) => Promise<T>
      default: <T = unknown>(
        serviceName: string,
        serverId: string,
        params: unknown,
        callBack?: GrpcCallback<T>,
      ) => Promise<T>
      submit: <T = unknown>(
        serviceName: string,
        serverId: string,
        params: unknown,
        callBack?: GrpcCallback<T>,
      ) => Promise<T>
      agent: <T = unknown>(
        serviceName: string,
        serverId: string,
        sessionId: string,
        modelType: string,
        isStreamResponse: boolean,
        messages: unknown,
        callBack: AgentCallbck<T>,
      ) => void
      getStatus: () => Promise<any>
      updateLinkerApi: (newLinkerApi: string) => Promise<any>
      restartApp: () => Promise<void>
      fileUpload: (params: any, callback: any) => Promise<any>
      uploadCsvFile: (params: any, callBack: any) => Promise<any>
    }
    windowControl: {
      minimize: () => void
      maximize: () => void
      close: () => void
      isMaximized: () => Promise<boolean>
      onMaximizedChange: (callback: (maximized: boolean) => void) => void
      removeMaximizedChangeListener: (callback: (maximized: boolean) => void) => void
      setLoginSize: () => void
      maximizeAfterLogin: () => void
    }
    cacheApi: {
      clearAppCache: () => Promise<any>
      clearStorageData: (options: any) => Promise<any>
    }
    __IS_MATT__: boolean
  }
}
