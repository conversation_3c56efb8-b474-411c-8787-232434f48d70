import CryptoJS from 'crypto-js'
export class FileService {
  async uploadFile(
    file: File,
    uploadBack?: (data: any) => void,
    callback?: (progress: number) => void,
    chunkSize: number = 1024 * 1024,
  ) {
    // console.log('uploading file', file, chunkSize)
    // 计算文件SHA256
    const totalSha256 = await this.calculateFileSHA256(file)
    // 发送元数据
    let chunkId = 0
    // 分块发送
    const chunkCount = Math.ceil(file.size / chunkSize)
    const reader = new FileReader()
    let progress = 0
    reader.onload = (e) => {
      const isLast = chunkId === chunkCount - 1
      const content = e.target?.result as ArrayBuffer
      if (content) {
        window.grpcApi.fileUpload(
          {
            fileName: file.name,
            sha256: totalSha256,
            chunkId: chunkId,
            content: new Uint8Array(content), // 使用八进制数组，处理二进制数据
            isLast: isLast,
          },
          (data) => {
            if (uploadBack) {
              uploadBack(data)
            }
          },
        )
      }

      progress = (++chunkId / chunkCount) * 100
      if (callback) {
        callback(progress)
      }
      if (chunkId < chunkCount) readNextChunk()
      else console.log('end of file')
    }

    function readNextChunk() {
      const start = chunkId * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      reader.readAsArrayBuffer(file.slice(start, end))
    }
    readNextChunk()
  }
  // 计算文件 SHA256 完整性的完整实现方案
  async calculateFileSHA256(file: File, chunkSize = 1024 * 1024): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      const hash = CryptoJS.algo.SHA256.create()
      let offset = 0
      // 分段读取文件并计算哈希
      const readChunk = () => {
        const slice = file.slice(offset, offset + chunkSize)
        reader.readAsArrayBuffer(slice)
      }

      reader.onload = (e) => {
        if (e.target?.result) {
          // 将ArrayBuffer转换为WordArray
          const wordArray = this.arrayBufferToWordArray(e.target.result as ArrayBuffer)
          hash.update(wordArray)

          offset += chunkSize
          if (offset < file.size) {
            readChunk() // 继续读取下一块
          } else {
            // 最终计算哈希
            const result = hash.finalize()
            resolve(result.toString(CryptoJS.enc.Hex))
          }
        }
      }

      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }

      readChunk() // 开始读取第一块
    })
  }

  // ArrayBuffer 转 CryptoJS WordArray
  arrayBufferToWordArray(buffer: ArrayBuffer): CryptoJS.lib.WordArray {
    const uint8Array = new Uint8Array(buffer)
    const len = uint8Array.length
    const words: number[] = []

    for (let i = 0; i < len; i++) {
      words[i >>> 2] |= uint8Array[i] << (24 - (i % 4) * 8)
    }

    return CryptoJS.lib.WordArray.create(words, len)
  }
}
