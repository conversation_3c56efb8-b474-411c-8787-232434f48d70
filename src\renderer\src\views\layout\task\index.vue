<template>
  <div class="flex flex-col h-full">
    <Container class="flex flex-col h-full">
      <div class="flex items-center justify-between flex-shrink-0">
        <HeaderTitle :title="t('tasks.title')" :tooltip="t('tasks.tooltip')" :show-icon="true" />
        <div class="flex items-center space-x-2">
          <label for="refresh-interval" class="text-sm">刷新时间间隔 (秒):</label>
          <Input
            v-model="refreshInterval"
            type="number"
            min="1"
            step="1"
            placeholder="刷新间隔"
            class="w-16 h-8"
          />
          <Button
            variant="outline"
            size="sm"
            :class="{
              'border-2 border-blue-500': refreshInterval !== currentRefreshInterval, // 动态添加高亮边框
              'border-gray-300': refreshInterval === currentRefreshInterval, // 正常边框
            }"
            @click="setRefreshTimer"
          >
            <RefreshCw class="w-4 h-4 mr-2" />
            {{ '应用' }}
          </Button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="py-2 flex flex-wrap gap-2 flex-shrink-0">
        <div class="relative flex-grow max-w-xl">
          <Search
            class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"
          />
          <Input
            v-model="searchQuery"
            placeholder="搜索任务编号..."
            class="pl-10 bg-gray-100 border-gray-200"
          />
        </div>

        <!-- 创建日期 dropdown -->
        <DropdownMenu v-model:open="isDateDropdownOpen">
          <DropdownMenuTrigger as-child>
            <Button variant="outline" class="bg-gray-100 flex items-center gap-2">
              <div class="flex items-center">
                <!-- <Calendar class="h-4 w-4 mr-2" /> -->
                {{ sortBy === 'latest' ? '最新优先' : '最早优先' }}
              </div>
              <ChevronDown class="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent class="w-[200px]">
            <DropdownMenuItem
              :class="{ 'bg-blue-500 text-white': sortBy === 'latest' }"
              @click="sortBy = 'latest'"
            >
              最新优先
            </DropdownMenuItem>
            <DropdownMenuItem
              :class="{ 'bg-blue-500 text-white': sortBy === 'oldest' }"
              @click="sortBy = 'oldest'"
            >
              最早优先
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <!-- 筛选 popover -->
        <Popover v-model:open="isFilterOpen">
          <PopoverTrigger as-child>
            <Button variant="outline" class="bg-gray-100">
              <Filter class="h-4 w-4 mr-2" />
              <span>筛选</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent class="w-[300px] p-0" align="start">
            <div class="p-4 border-b">
              <h3 class="font-bold text-lg mb-4">筛选</h3>
              <div class="space-y-4">
                <div>
                  <h4 class="text-sm text-gray-500 mb-2">按状态</h4>
                  <RadioGroup v-model="statusFilter" class="flex flex-wrap gap-4">
                    <div class="flex items-center space-x-2">
                      <RadioGroupItem id="status-all" value="all" class="border-black" />
                      <Label for="status-all">全部</Label>
                    </div>
                    <div
                      v-for="status in statusOptions"
                      :key="status"
                      class="flex items-center space-x-2"
                    >
                      <RadioGroupItem :id="`status-${status}`" :value="status" />
                      <Label :for="`status-${status}`">
                        {{ taskStore.getStatusText(status as TaskStatus) }}
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div>
                  <h4 class="text-sm text-gray-500 mb-2">按日期</h4>
                  <Select v-model="dateFilter">
                    <SelectTrigger class="w-full bg-gray-100">
                      <SelectValue placeholder="全部" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="last7days">最近7天</SelectItem>
                      <SelectItem value="last30days">最近30天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        <!-- 重置筛选按钮 -->
        <Button
          variant="outline"
          class="bg-gray-100"
          :disabled="!hasActiveFilters"
          @click="resetFilters"
        >
          <RotateCcw class="h-4 w-4 mr-2" />
          <span>重置</span>
        </Button>
      </div>
      <!-- 表格 -->
      <div class="w-full overflow-hidden flex flex-1 flex-col">
        <DataTable
          table-id="taskList"
          :data="paginatedTasks"
          :columns="columns"
          max-height="calc(100vh - 180px)"
        >
          <!-- 状态插槽 -->
          <template #status="{ item }: { item: Task }">
            <TableCell class="max-w-24">
              <Badge :class="taskStore.getStatusClass(item.taskStatus)">
                {{ taskStore.getStatusText(item.taskStatus) }}
              </Badge>
            </TableCell>
          </template>
          <!-- 进度条插槽 -->
          <template #[task_process]="{ item }: { item: Task }">
            <TableCell class="max-w-24">
              <div class="flex flex-row items-center justify-center gap-1">
                <span class="w-2/3">
                  <Progress :model-value="item.taskProcess" class="w-full" />
                </span>
                <span class="w-1/3 text-xs text-gray-500">
                  {{ typeof item.taskProcess === 'number' ? item.taskProcess.toFixed(2) : '--' }}%
                </span>
              </div>
            </TableCell>
          </template>
          <!-- 结果插槽-->
          <template #result="{ item }: { item: Task }">
            <TableCell class="max-w-12">
              <div class="flex space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  title="查看结果"
                  :disabled="item.taskStatus !== 'Finished'"
                  @click="viewTaskResult(item)"
                >
                  <Eye class="w-4 h-4" stroke-width="2.5" />
                </Button>
              </div>
            </TableCell>
          </template>
          <!-- 操作插槽 -->
          <template #actions="{ item }: { item: Task }">
            <TableCell class="max-w-24">
              <div class="flex space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  title="停止"
                  :disabled="!['Computing', 'Paused'].includes(item.taskStatus)"
                  @click="stopTask(item)"
                >
                  <XCircle class="w-4 h-4" color="red" stroke-width="2.5" />
                </Button>
                <Button
                  v-show="item.taskStatus !== 'Paused'"
                  variant="ghost"
                  size="icon"
                  title="暂停"
                  :disabled="item.taskStatus !== 'Computing'"
                  @click="handleTaskAction(item, 'pause')"
                >
                  <CirclePause class="w-4 h-4" color="#FFCC00" stroke-width="2.5" />
                </Button>
                <Button
                  v-show="item.taskStatus === 'Paused'"
                  variant="ghost"
                  size="icon"
                  title="恢复"
                  @click="handleTaskAction(item, 'resume')"
                >
                  <CirclePlay class="w-4 h-4" color="green" stroke-width="2.5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  title="删除"
                  :disabled="!['Finished', 'Error', 'Abort'].includes(item.taskStatus)"
                  @click="deleteTask(item)"
                >
                  <Trash class="w-4 h-4" stroke-width="2.5" color="red" />
                </Button>
              </div>
            </TableCell>
          </template>
        </DataTable>
      </div>

      <!-- 分页 -->
      <div class="flex justify-between items-center text-sm mt-2 flex-shrink-0">
        <div class="flex items-center space-x-2">
          <span>显示</span>
          <DropdownMenu v-model:open="isPageSizeDropdownOpen">
            <DropdownMenuTrigger as-child>
              <Button variant="outline" class="h-7 px-2 py-0 bg-gray-100 w-20">
                {{ pageSize }} 条
                <ChevronDown class="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent class="w-[100px]">
              <DropdownMenuItem
                v-for="size in [5, 10, 20, 50, 100]"
                :key="size"
                :class="{ 'bg-primary text-primary-foreground': pageSize === size }"
                @click="pageSize = size"
              >
                {{ size }} 条
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <span>共 {{ totalTasks }} 条</span>
        </div>
        <div class="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            class="h-7 w-7"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            <ChevronLeft class="h-4 w-4" />
          </Button>
          <span class="px-3 py-1 bg-gray-200 rounded-md">{{ currentPage }}</span>
          <span>共 {{ totalPages }} 页</span>
          <Button
            variant="outline"
            size="icon"
            class="h-7 w-7"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            <ChevronRight class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Container>
    <!-- 详情 -->
    <LogDetail
      :is-open="isLogDetailOpen"
      :log-id="currentLogId"
      @update:is-open="isLogDetailOpen = $event"
    />
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'

import { useTaskStore } from '@renderer/store'
import { useLanguage } from '@renderer/config/hooks'

import { useConfirmDialog } from '@renderer/components/dialog/useConfirmDialog'
import { Container, HeaderTitle, DataTable } from '@renderer/components'
import { LogDetail } from '@renderer/components'
import { toast } from 'vue-sonner'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { TaskStatus, Task } from '@renderer/config/types/api/task'
import { formatDate } from '@renderer/utils/utils'

const { t } = useLanguage()

import {
  Eye,
  XCircle,
  Trash,
  CirclePause,
  CirclePlay,
  RefreshCw,
  Search,
  RotateCcw,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Filter,
} from 'lucide-vue-next'

const { openConfirm } = useConfirmDialog()
// 使用任务 store
const taskStore = useTaskStore()
const taskService = createTaskService()
const isLogDetailOpen = ref(false)
const currentLogId = ref<string | undefined>(undefined)

let refreshTimer: number | null = null
const refreshInterval = ref<number>(taskStore.refreshInterval)
const currentRefreshInterval = ref<number>(5)
const taskLogsMap = ref<Record<string, boolean>>({}) // 是否有对应日志
const taskLoadingMap = ref<Record<'pause' | 'resume', Set<string>>>({
  pause: new Set(),
  resume: new Set(),
})

// 搜索和筛选状态
const searchQuery = ref('')
const statusFilter = ref('all')
const isFilterOpen = ref(false)
const isDateDropdownOpen = ref(false)
const isPageSizeDropdownOpen = ref(false)
const sortBy = ref('latest')
const dateFilter = ref('all')

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)

// 状态选项
const statusOptions = [
  'Computing',
  'Paused',
  'Finished',
  'Error',
  'Abort',
  'Pending',
  'Initializing',
  'TaskStay',
] as const
type StatusOption = (typeof statusOptions)[number]

// 日期范围常量
const DATE_RANGES = {
  all: 0,
  today: 24 * 60 * 60 * 1000,
  last7days: 7 * 24 * 60 * 60 * 1000,
  last30days: 30 * 24 * 60 * 60 * 1000,
} as const

// 插槽参数
const task_process = 'taskProcess'

// 表格列配置
const columns = [
  {
    title: '任务编号',
    field: 'taskId',
    copyable: true,
  },
  {
    title: '运行状态',
    field: 'status',
    useSlot: true,
  },
  {
    title: '提交时间',
    field: 'createTime',
    formatter: (value: number) => formatDate(new Date(value * 1000 || Date.now())),
    //formatter: (value: number) => new Date(value * 1000).toLocaleString(),
  },
  {
    title: '运行时间',
    field: 'duration',
  },
  {
    title: '进度',
    field: 'taskProcess',
    useSlot: true,
  },
  {
    title: '结果',
    field: 'result',
    useSlot: true,
  },
  {
    title: '操作',
    field: 'actions',
    useSlot: true,
  },
] as const

// 过滤和排序后的任务列表
const filteredTasks = computed(() => {
  return taskStore.tasks
    .filter((task) => {
      // 按任务ID搜索
      if (
        searchQuery.value &&
        !task.taskId.toLowerCase().includes(searchQuery.value.toLowerCase())
      ) {
        return false
      }

      // 按状态筛选
      if (statusFilter.value !== 'all' && task.taskStatus !== statusFilter.value) {
        return false
      }

      // 按日期筛选
      if (dateFilter.value !== 'all') {
        const now = new Date().getTime()
        const taskDate = new Date(task.createTime * 1000).getTime()
        const range = DATE_RANGES[dateFilter.value as keyof typeof DATE_RANGES]

        if (dateFilter.value === 'today') {
          // 今天的任务
          const startOfToday = new Date()
          startOfToday.setHours(0, 0, 0, 0)
          if (taskDate < startOfToday.getTime()) {
            return false
          }
        } else {
          // 最近几天的任务
          const startTime = now - range
          if (taskDate < startTime) {
            return false
          }
        }
      }

      return true
    })
    .sort((a, b) => {
      // 按提交时间排序
      if (sortBy.value === 'latest') {
        return b.createTime - a.createTime
      } else {
        return a.createTime - b.createTime
      }
    })
})

// 计算总任务数和总页数
const totalTasks = computed(() => filteredTasks.value.length)
const totalPages = computed(() => Math.max(1, Math.ceil(totalTasks.value / pageSize.value)))

// 当前页的任务
const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTasks.value.slice(start, end)
})

// 判断是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return (
    searchQuery.value !== '' ||
    statusFilter.value !== 'all' ||
    dateFilter.value !== 'all' ||
    sortBy.value !== 'latest'
  )
})
// 重置所有筛选条件
const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = 'all'
  dateFilter.value = 'all'
  sortBy.value = 'latest'
  currentPage.value = 1
}

// 操作函数
const viewTaskResult = (task: Task) => {
  currentLogId.value = task.taskId
  isLogDetailOpen.value = true
}

const stopTask = async (task: Task) => {
  const id = task.taskId
  openConfirm({
    title: '终止任务',
    description: '任务将被终止, 你将无法恢复任务。',
    confirmText: '确认',
    cancelText: '取消',
    onConfirm: async () => {
      const res = await taskService.stopTask(id)
      if (res.status === 'Success') {
        console.log('stop 任务成功', res)
      } else {
        console.error('stop 任务失败:', res.message)
      }
      await taskStore.updateTaskList()
    },
  })
}

const handleTaskAction = async (task: Task, action: 'pause' | 'resume') => {
  const id = task.taskId
  const loadingSet = taskLoadingMap.value[action]

  if (loadingSet.has(id)) return // 防止重复点击

  loadingSet.add(id)
  const res = await taskService[action](id)
  if (res.status === 'Success') {
    console.log(`${action} 任务成功`, res)
  } else {
    console.error(`${action} 任务失败:`, res.message)
  }

  await taskStore.updateTaskList()
  loadingSet.delete(id)
}

const deleteTask = (task: Task) => {
  const id = task.taskId
  openConfirm({
    title: '删除任务',
    description: '任务将被删除, 请保存任务结果。',
    confirmText: '确认',
    cancelText: '取消',
    onConfirm: async () => {
      await taskService.deleteTask(id)
      toast.info('任务已删除')

      await taskStore.updateTaskList()
    },
  })
}

// 刷新任务列表
const refreshTaskList = useDebounceFn(async () => {
  // console.log('refreshTaskList', new Date().toLocaleTimeString())
  // if (taskStore.loading) return
  try {
    await taskStore.updateTaskList()
  } catch (error) {
    console.error('刷新任务列表失败:', error)
  }
}, 1000)

const setRefreshTimer = () => {
  if (refreshInterval.value < 2) {
    // 还原到当前的刷新时间
    refreshInterval.value = currentRefreshInterval.value
    toast.info('刷新间隔时间请大于2s')
    return
  }

  if (refreshTimer !== null) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  refreshTaskList()
  refreshTimer = window.setInterval(() => {
    refreshTaskList()
  }, refreshInterval.value * 1000)
  taskStore.setRefreshInterval(refreshInterval.value)
  currentRefreshInterval.value = refreshInterval.value
}

// 在组件挂载时调用
onMounted(async () => {
  refreshInterval.value = taskStore.refreshInterval
  currentRefreshInterval.value = refreshInterval.value
  taskStore.updateTaskList()
  setRefreshTimer()
})

onUnmounted(() => {
  if (refreshTimer !== null) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 监听筛选条件变化，重置页码
watch([searchQuery, statusFilter, dateFilter, sortBy], () => {
  currentPage.value = 1
})
// 监听页面大小变化，重置页码
watch(pageSize, () => {
  currentPage.value = 1
})
</script>
<style lang="scss" scoped>
:deep(table) {
  table-layout: fixed;
  width: 100%;
}

:deep(th),
:deep(td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.badge-pending {
  @apply bg-gray-500;
}
.badge-running {
  @apply bg-blue-500;
}
.badge-completed {
  @apply bg-green-500;
}
.badge-failed {
  @apply bg-red-500;
}

// 状态标签样式
:deep(.bg-green-100) {
  @apply bg-opacity-20;
}

:deep(.bg-red-100) {
  @apply bg-opacity-20;
}

:deep(.bg-gray-100) {
  @apply bg-opacity-20;
}

// 调整按钮大小
:deep(.button-sm) {
  @apply h-7 w-7;
}
</style>
