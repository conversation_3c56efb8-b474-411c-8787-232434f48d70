import { ipcMain } from 'electron'
// import { LinkerClient } from '../grpc/clientFactory'
import { Worker } from 'worker_threads'
import { configService } from '../services/configService'
import { GrpcService } from '../services/grpcService'
import logger from '../utils/logger'

// 跟踪处理程序是否已设置
let handlersSetup = false
/**
 * 设置gRPC相关的IPC处理程序
 */
export function setupGrpcHandlers(grpcService: GrpcService): void {
  // 如果处理程序已设置，则直接返回
  if (handlersSetup) {
    logger.info('gRPC IPC处理程序已设置，跳过重复设置')
    return
  }
  // gRPC调用
  ipcMain.handle('grpc-call', async (_event, { apiName, params }) => {
    try {
      // logger.info(`gRPC call 调用: ${apiName}`, params)
      return await grpcService.call(apiName, params)
    } catch (error) {
      logger.error(`gRPC call 错误 (${apiName}):`, error)
      throw error
    }
  })

  // gRPC提交同步任务
  ipcMain.handle('grpc-default', async (_event, { serviceName, serverId, isSave, params }) => {
    try {
      logger.info(`gRPC 提交同步任务: ${serviceName}`, params)
      return await grpcService.default(serviceName, serverId, isSave, params)
    } catch (error) {
      logger.error(`gRPC 提交同步任务失败 (${serviceName}):`, error)
      throw error
    }
  })

  // gRPC提交计算任务
  ipcMain.handle('grpc-submit', async (_event, { serviceName, serverId, isSave, params }) => {
    try {
      logger.info(`gRPC 提交计算任务: ${serviceName}`, params)
      return await grpcService.submit(serviceName, serverId, isSave, params)
    } catch (error) {
      logger.error(`gRPC 提交计算任务失败 (${serviceName}):`, error)
      throw error
    }
  })

  // gRPC Agent
  ipcMain.on(
    'grpc-agent',
    (event, { serviceName, serverId, sessionId, modelType, isStreamResponse, messages }) => {
      grpcService.agent(
        serviceName,
        serverId,
        sessionId,
        modelType,
        isStreamResponse,
        messages,
        (result, type) => {
          if (type === 'data') {
            event.sender.send(sessionId, { type, data: result })
          } else if (type === 'end') {
            event.sender.send(sessionId, { type })
          } else if (type === 'error') {
            event.sender.send(sessionId, { type, error: result.message || String(result) })
          } else {
            // 兼容非流式（或初始响应）
            event.sender.send(sessionId, { type, data: result })
          }
        },
      )
    },
  )

  // setClient
  ipcMain.on('grpc-setClient', (event, { userName, loginIp, password, expiredTime }) => {
    // 使用固定的通道名称 'client-channel-event'
    grpcService.setClient(userName, loginIp, password, expiredTime, (data, type) => {
      // 根据类型发送不同的消息
      if (type === 'data') {
        event.sender.send('client-channel-event', {
          type,
          data,
        })
      } else if (type === 'end') {
        event.sender.send('client-channel-event', { type })
      } else if (type === 'error') {
        const errorMsg =
          data instanceof Error
            ? data.message
            : typeof data === 'object'
              ? JSON.stringify(data)
              : String(data)

        event.sender.send('client-channel-event', {
          type,
          error: errorMsg,
        })
      }
    })
  })
  // Client-side streaming RPC：客户端流式 RPC
  // fileName,
  // sha256,
  // chunkId,
  // content,
  // isLast,
  ipcMain.on('grpc-client-stream', (event, { fileName, sha256, chunkId, content, isLast }) => {
    // 建立连接
    grpcService.clientSideStream(fileName, sha256, chunkId, content, isLast, (data, type) => {
      event.reply('client-upload-status', {
        type,
        data,
      })
    })
  })

  // 获取gRPC连接状态
  ipcMain.handle('get-grpc-status', () => {
    return grpcService.getStatus()
  })

  // 更新认证信息
  ipcMain.handle('update-auth-info', async (_event, { userId, token }) => {
    try {
      logger.info(`接收到认证信息更新请求: userId=${userId}`)
      return grpcService.updateAuthInfo(userId, token)
    } catch (error) {
      logger.error('更新认证信息失败:', error)
      return { success: false, message: `更新失败: ${(error as Error).message}` }
    }
  })

  // 停止消息生成
  ipcMain.on('grpc-stop-generation', (_, { sessionId }) => {
    try {
      logger.info(`收到停止生成请求，会话ID: ${sessionId}`)
      grpcService.stopGeneration(sessionId)
    } catch (error) {
      logger.error(`处理停止生成请求失败: ${error}`)
    }
  })
  // 主进程监听来自渲染进程的上传csv数据的请求
  ipcMain.on('upload-csv-file', async (event, meta) => {
    // logger.info(meta)
    // const uploadId = meta.uploadId
    // const dataType = meta.dataType
    // const serverUrl =
    //   configService.getLinkerApiFromConfig() || process.env.VITE_APP_LINKER_API || ''
    // const { port1: mainPort, port2: workerPort } = new MessageChannel()
    // const data = {
    //   filePath: meta.filePath,
    //   uploadId: uploadId,
    //   userId: meta.userId,
    //   serverUrl: serverUrl,
    //   type: dataType,
    //   port: workerPort,
    // }
    // const linkerClient = new LinkerClient(serverUrl, 'LinkerService', {})
    // logger.info('linkerClient:', linkerClient)
    // const metadata = new grpc.Metadata()
    // metadata.add('method', 'uploadFile')
    // metadata.add('source', 'client')
    // // 监听进度更新
    // await threadPool.runTask('analysisUploadFile', data, [workerPort])
    // // 上传的逻辑现在修改在这里，worker现在的处理数据之后，将处理好的数据发送给主进程，由主进程直接
    // // 由于之前业务设计是需要上传两次，考虑到上传的内容过大，所以上传逻辑留在这里，上传过程中处理文件和分段放在worker中处理
    // // let streamCsv: any = null // 圈数文件
    // // let streamCapacity: any = null // 容量文件
    // mainPort.on('message', (data: any) => {
    //   if (data.type === 'data') {
    //     // if (!streamCsv) {
    //     //   streamCsv = linkerClient.call(
    //     //     'uploadFile',
    //     //     data,
    //     //     metadata,
    //     //     (res) => {
    //     //       console.log(res)
    //     //     },
    //     //     'clientStream',
    //     //   )
    //     // }
    //     logger.info(data.type)
    //   } else if (data.type === 'capacity') {
    //     // if (!streamCapacity) {
    //     //   streamCapacity = linkerClient.call(
    //     //     'uploadFile',
    //     //     data,
    //     //     metadata,
    //     //     (res) => {
    //     //       console.log(res)
    //     //     },
    //     //     'clientStream',
    //     //   )
    //     // }
    //     logger.info(data.type)
    //   }
    //   logger.info(`任务进度:=====%`)
    // })
    try {
      const serverUrl =
        configService.getLinkerApiFromConfig() || process.env.VITE_APP_LINKER_API || ''
      const workerUrl = new URL('./workers/handleUploadCsvFileWorker.mjs', import.meta.url)
      const uploadId = meta.uploadId
      const dataType = meta.dataType
      const worker = new Worker(workerUrl, {
        workerData: {
          filePath: meta.filePath,
          uploadId: uploadId,
          userId: meta.userId,
          serverUrl: serverUrl,
          type: dataType,
        },
      })
      worker.stdout.on('data', (data) => {
        logger.log(`[Worker stdout]: ${data}`)
      })
      worker.stderr.on('data', (data) => {
        logger.log(`[Worker stderr]: ${data}`)
      })
      worker.on('message', (msg) => {
        event.reply('upload-status', msg)
      })
      worker.on('error', (err) => {
        logger.error(`[UploadWorker ${meta.uploadId}] Error:`, err)
      })
      worker.on('exit', (code) => {
        if (code !== 0) {
          logger.error(`[UploadWorker ${meta.uploadId}] Exited with code ${code}`)
        }
      })
    } catch (err) {
      logger.error(`[UploadWorker ${meta.uploadId}] Failed to start worker:`, err)
    }
  })

  // 标记处理程序已设置
  handlersSetup = true
  logger.info('gRPC IPC处理程序已设置')
}
