<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="inChannels" class="text-sm font-medium">inChannels</Label>
                <Input
                  id="inChannels"
                  v-model="inChannels"
                  type="number"
                  min="1"
                  placeholder="请输入输入通道数"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="channels" class="text-sm font-medium">channels</Label>
                <Input
                  id="channels"
                  v-model="channels"
                  type="number"
                  min="1"
                  placeholder="请输入通道数"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentHeight" class="text-sm font-medium">
                  input_segment_height
                </Label>
                <Input
                  id="inputSegmentHeight"
                  v-model="inputSegmentHeight"
                  type="number"
                  min="1"
                  placeholder="请输入输入段高度"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentWidth" class="text-sm font-medium">
                  input_segment_width
                </Label>
                <Input
                  id="inputSegmentWidth"
                  v-model="inputSegmentWidth"
                  type="number"
                  min="1"
                  placeholder="请输入输入段宽度"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="alpha" class="text-sm font-medium">alpha</Label>
                <Input
                  id="alpha"
                  v-model="alpha"
                  type="number"
                  step="0.1"
                  placeholder="请输入alpha值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="kernelSize" class="text-sm font-medium">kernel_size</Label>
                <Input
                  id="kernelSize"
                  v-model="kernelSize"
                  type="number"
                  min="1"
                  placeholder="请输入核大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="trainSupportSize" class="text-sm font-medium">train_support_size</Label>
                <Input
                  id="trainSupportSize"
                  v-model="trainSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入训练支持大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="testSupportSize" class="text-sm font-medium">test_support_size</Label>
                <Input
                  id="testSupportSize"
                  v-model="testSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入测试支持大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="actFn" class="text-sm font-medium">act_fn</Label>
                <Select id="actFn" v-model="actFnDefault" class="w-full">
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择激活函数" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="choice in props.modelParams.actFn?.choices || []"
                      :key="choice"
                      :value="choice"
                    >
                      {{ choice }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- 布尔值参数 -->
              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">use_fc_for_prediction</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="useFcForPrediction"
                    v-model="useFcForPrediction"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="useFcForPrediction" class="text-sm">启用FC预测</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">filter_cycles_flag</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="filterCyclesFlag"
                    v-model="filterCyclesFlag"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="filterCyclesFlag" class="text-sm">过滤循环标志</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">return_pointwise_predictions</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="returnPointwisePredictions"
                    v-model="returnPointwisePredictions"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="returnPointwisePredictions" class="text-sm">返回逐点预测</Label>
                </div>
              </div>

              <!-- 字符串参数 -->
              <div class="flex flex-col space-y-2">
                <Label for="featuresToDrop" class="text-sm font-medium">features_to_drop</Label>
                <Input
                  id="featuresToDrop"
                  v-model="featuresToDrop"
                  placeholder="请输入要丢弃的特征"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="cyclesToDropInSegment" class="text-sm font-medium">
                  cycles_to_drop_in_segment
                </Label>
                <Input
                  id="cyclesToDropInSegment"
                  v-model="cyclesToDropInSegment"
                  placeholder="请输入段中要丢弃的循环"
                  class="h-10 w-full"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { LucideIcon } from '@renderer/components'

const props = defineProps({
  modelParams: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:modelParams'])

// 为每个字段创建计算属性以支持 v-model
const inChannels = computed({
  get: () => props.modelParams.inChannels,
  set: (value) => emit('update:modelParams', { ...props.modelParams, inChannels: value }),
})

const channels = computed({
  get: () => props.modelParams.channels,
  set: (value) => emit('update:modelParams', { ...props.modelParams, channels: value }),
})

const inputSegmentHeight = computed({
  get: () => props.modelParams.inputSegmentHeight,
  set: (value) => emit('update:modelParams', { ...props.modelParams, inputSegmentHeight: value }),
})

const inputSegmentWidth = computed({
  get: () => props.modelParams.inputSegmentWidth,
  set: (value) => emit('update:modelParams', { ...props.modelParams, inputSegmentWidth: value }),
})

const alpha = computed({
  get: () => props.modelParams.alpha,
  set: (value) => emit('update:modelParams', { ...props.modelParams, alpha: value }),
})

const kernelSize = computed({
  get: () => props.modelParams.kernelSize,
  set: (value) => emit('update:modelParams', { ...props.modelParams, kernelSize: value }),
})

const trainSupportSize = computed({
  get: () => props.modelParams.trainSupportSize,
  set: (value) => emit('update:modelParams', { ...props.modelParams, trainSupportSize: value }),
})

const testSupportSize = computed({
  get: () => props.modelParams.testSupportSize,
  set: (value) => emit('update:modelParams', { ...props.modelParams, testSupportSize: value }),
})

const actFnDefault = computed({
  get: () => props.modelParams.actFn?.default,
  set: (value) =>
    emit('update:modelParams', {
      ...props.modelParams,
      actFn: { ...props.modelParams.actFn, default: value },
    }),
})

const useFcForPrediction = computed({
  get: () => props.modelParams.useFcForPrediction,
  set: (value) => emit('update:modelParams', { ...props.modelParams, useFcForPrediction: value }),
})

const filterCyclesFlag = computed({
  get: () => props.modelParams.filterCyclesFlag,
  set: (value) => emit('update:modelParams', { ...props.modelParams, filterCyclesFlag: value }),
})

const returnPointwisePredictions = computed({
  get: () => props.modelParams.returnPointwisePredictions,
  set: (value) =>
    emit('update:modelParams', { ...props.modelParams, returnPointwisePredictions: value }),
})

const featuresToDrop = computed({
  get: () => props.modelParams.featuresToDrop,
  set: (value) => emit('update:modelParams', { ...props.modelParams, featuresToDrop: value }),
})

const cyclesToDropInSegment = computed({
  get: () => props.modelParams.cyclesToDropInSegment,
  set: (value) =>
    emit('update:modelParams', { ...props.modelParams, cyclesToDropInSegment: value }),
})
</script>

<style></style>
