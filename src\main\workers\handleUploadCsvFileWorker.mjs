import grpc from '@grpc/grpc-js'
import protoLoader from '@grpc/proto-loader'
import crypto from 'crypto'
import * as fs from 'fs'
import iconv from 'iconv-lite'
import Papa from 'papaparse'
import * as path from 'path'
import { Readable } from 'stream'
import { parentPort, workerData } from 'worker_threads'

const { filePath, uploadId, userId, serverUrl, type } = workerData
const headerList = ['cycle', 'time', 'current', 'voltage', 'capacity']
const headerType = ['float', 'float', 'float', 'float', 'float']
const headerUnit = ['/', 's', 'A', 'V', 'mAh']
const unit_value = {
  mA: 1e-3,
  mV: 1e-3,
  Ah: 1000,
  A: 1,
  V: 1,
  mAh: 1,
  '': 1,
}

const isSave = false

// 处理CSV类型数据
const handleUploadCsvFile = async () => {
  try {
    sendMsg('read', 0, 'processing')
    const fileName = path.basename(filePath)
    readAllFile(filePath, async (dataList) => {
      if (dataList.length > 0) {
        sendMsg('read', 100, 'finished')
        await headerToFile(dataList, fileName)
      }
    })
  } catch (err) {
    sendMsg('read', 0, 'failed', {}, err.message)
  }
}

//const sendMsg = (data) => parentPort?.postMessage(data)

const sendMsg = (
  step,
  progress = 0,
  status = 'success',
  data = {},
  message = '',
  dataType = type,
) => {
  parentPort?.postMessage({
    uploadId,
    step,
    progress,
    status,
    data,
    message,
    dataType,
  })
}

// 根据表头信息，我们把新威，蓝电两种格式的文件，转化成我们自己的格式
// const neware = ['循环号', '电流(A)', '电压(V)', '容量(Ah)', '绝对时间'] // 新威
// const lanhe = ['电流/A', '容量/Ah', '电压/V', '系统时间', '循环序号'] // 蓝电
// 将内容中添加上表头
const headerToFile = async (dataList, fileName) => {
  try {
    sendMsg('processData', 0, 'processing')

    const headerCol = dataList[0]
    const fileType = getFileType(headerCol.join(''))
    //const fileType = 'SOC-OCV'
    // 如果是默认格式，直接转化为内容数据返回
    if (fileType === 'default') {
      await uploadData(filePath, 'file', fileName, serverUrl, userId)
      const rowCount = dataList.length - 3
      const currentData = new Float32Array(rowCount)
      const voltageData = new Float32Array(rowCount)
      const timeData = new Float32Array(rowCount)
      const contentRows = dataList.slice(3)
      const headerMap = new Map()
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        if (header)
          headerMap.set(header, {
            idx: i,
            isTime: header === 'time',
            isCapacity: header === 'capacity',
            isCurrent: header === 'current',
            isVoltage: header == 'voltage',
          })
      }
      const lastRow = contentRows[contentRows.length - 1]
      const maxCycle = parseInt(lastRow[headerMap.get('cycle')?.idx])
      const capacityData = new Float32Array(maxCycle)

      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        const currentCycle = row[headerMap.get('cycle')?.idx] - 1
        for (let j = 0; j < headerList.length; j++) {
          const key = headerList[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          if (mapVal.isTime) {
            const [h, m, s] = rawValue.split(':')
            const date = new Date(0)
            date.setUTCHours(h)
            date.setUTCMinutes(m)
            date.setUTCSeconds(s)
            timeData[i] = date.getTime()
          } else if (mapVal.isCapacity) {
            if (rawValue > capacityData[currentCycle]) {
              capacityData[currentCycle] = Math.round(rawValue * 1e-3 * 100) / 100
            }
          } else {
            if (mapVal.isCurrent) {
              currentData[i] = rawValue
            } else if (mapVal.isVoltage) {
              voltageData[i] = rawValue
            }
          }
        }
      }

      sendMsg('processData', 100, 'finished', {
        current: currentData,
        voltage: voltageData,
        time: timeData,
        capacity: capacityData,
      })
      if (type === 'CycleData') {
        await uploadCapacityData(capacityData, fileName)
      }
      return
    } else if (fileType === 'SOC-OCV') {
      await uploadData(filePath, 'file', fileName, serverUrl, userId)
      const headerMap = new Map()
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        if (header)
          headerMap.set(header, {
            idx: i,
            isSoc: header === 'soc',
            isOcv: header === 'ocv',
          })
      }
      const rowCount = dataList.length - 1
      const sococvData = new Array(rowCount)
      // const socData = new Float32Array(rowCount)
      // const ocvData = new Float32Array(rowCount)
      const contentRows = dataList.slice(1)
      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        let soc = null
        let ocv = null

        for (let j = 0; j < headerCol.length; j++) {
          const key = headerCol[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          if (mapVal.isSoc) {
            soc = parseFloat(rawValue)
          } else if (mapVal.isOcv) {
            ocv = parseFloat(rawValue)
          }
        }
        sococvData[i] = [soc, ocv]
      }
      sendMsg('processData', 100, 'finished', {
        sococv: sococvData,
      })
      return
    } else {
      const rowCount = dataList.length - 1
      const defaultData = new Array(rowCount + 3)
      const currentData = new Float32Array(rowCount)
      const voltageData = new Float32Array(rowCount)
      const timeData = new Float32Array(rowCount)
      const contentRows = dataList.slice(1)
      const headerMap = new Map()

      // 建立字段映射，识别各列对应字段
      for (let i = 0; i < headerCol.length; i++) {
        const header = headerCol[i]
        const unit = extractUnit(header)
        let key = ''
        if (header.includes('循环')) key = 'cycle'
        if (header.includes('时间')) key = 'time'
        if (header.includes('电流')) key = 'current'
        if (header.includes('电压')) key = 'voltage'
        if (header.includes('容量')) key = 'capacity'
        const unitFactor = unit_value[unit] || 1
        if (key)
          headerMap.set(key, {
            idx: i,
            unit: unitFactor,
            isTime: key === 'time',
            isCapacity: key === 'capacity',
            isCurrent: key === 'current',
            isVoltage: key == 'voltage',
          })
      }
      const lastRow = contentRows[contentRows.length - 1]
      const maxCycle = parseInt(lastRow[headerMap.get('cycle')?.idx])
      const capacityData = new Float32Array(maxCycle)

      // 相对时间计算基准
      let initTime = null
      const timeColumnIndex = headerMap.get('time')?.idx
      if (timeColumnIndex !== undefined)
        initTime = new Date(contentRows[0][timeColumnIndex]).getTime()

      sendMsg('processData', 10, 'processing')

      for (let i = 0; i < rowCount; i++) {
        const row = contentRows[i]
        const newRow = []
        const currentCycle = row[headerMap.get('cycle')?.idx] - 1
        for (let j = 0; j < headerList.length; j++) {
          const key = headerList[j]
          const mapVal = headerMap.get(key)
          if (!mapVal) continue
          const rawValue = row[mapVal.idx]
          const unitFactor = mapVal.unit
          if (mapVal.isTime && initTime !== null) {
            const relativeTime = new Date(rawValue).getTime() - initTime
            timeData[i] = relativeTime
            newRow.push(formatTimeDiff(relativeTime))
          } else if (mapVal.isCapacity) {
            const capacityValue = Math.round(rawValue * unitFactor * 100) / 100
            newRow.push(capacityValue)
            if (capacityValue > capacityData[currentCycle]) {
              capacityData[currentCycle] = capacityValue
            }
          } else {
            const value = rawValue * unitFactor
            newRow.push(value)
            if (mapVal.isCurrent) {
              currentData[i] = value
            } else if (mapVal.isVoltage) {
              voltageData[i] = value
            }
          }
        }
        defaultData[i + 3] = newRow
      }
      sendMsg('processData', 80, 'processing')
      // 添加标准头部信息
      defaultData[0] = headerList
      defaultData[1] = headerType
      defaultData[2] = headerUnit

      //const contentData = addHeaderKey(headerList, defaultData.slice(3))

      const csvData = convertToCSV(defaultData)
      sendMsg('processData', 100, 'finished', {
        current: currentData,
        voltage: voltageData,
        time: timeData,
        capacity: capacityData,
      })
      if (isSave) {
        saveFile(csvData, fileName)
      }

      // await uploadData(csvData, 'data', fileName, serverUrl, userId)
      // if (fileType === 'neware' || fileType === 'lanhe') {
      //   await uploadCapacityData(capacityData, fileName)
      // }
    }

    //await uploadStringData2(csvdata, fileName, serverUrl, userId)
  } catch (error) {
    sendMsg('processData', 0, 'error', {}, '处理文件时发生错误: ' + error.message)
  }
}

const uploadCapacityData = async (data, basefileName) => {
  const capacityCsvData = 'cycle,capacity\n' + conver1DArrayToCSV(data)
  const capacity_file_name = 'capacity_' + basefileName
  await uploadData(
    capacityCsvData,
    'data',
    capacity_file_name,
    serverUrl,
    userId,
    1024 * 1024,
    'Capacity',
  )
}

const getFileType = (header) => {
  if (header.includes('ocv') && header.includes('soc')) {
    return 'SOC-OCV'
  }
  const neware = ['循环号', '电流', '电压', '容量', '绝对时间'] // 新威
  const lanhe = ['电流', '容量', '电压', '系统时间', '循环序号'] // 蓝电

  // 统计匹配的关键词数量
  const newWareCount = neware.filter((keyword) => header.includes(keyword)).length
  const lanHeCount = lanhe.filter((keyword) => header.includes(keyword)).length

  // 判断类型
  if (newWareCount >= 4) return 'neware'
  if (lanHeCount >= 4) return 'lanhe'
  return 'default'
}

// 提取单位
const extractUnit = (header) => {
  const unit = header.replace(/[^a-zA-Z]/g, '')
  return unit || ''
}
// 将时间差转化成 【时：分：秒】的格式
const formatTimeDiff = (milliseconds) => {
  // 计算总秒数
  const totalSeconds = Math.floor(milliseconds / 1000)
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  // 格式化为两位数
  const pad = (num) => num.toString().padStart(2, '0')

  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
}
// 读取文件
const readAllFile = (filePath, callback) => {
  try {
    const READ_CHUNK_SIZE = 10 * 1024 * 1024

    const buffer = fs.readFileSync(filePath)
    const hasUtf8Bom =
      buffer.length >= 3 && buffer[0] === 0xef && buffer[1] === 0xbb && buffer[2] === 0xbf
    const csvBuffer = hasUtf8Bom ? buffer.slice(3) : buffer
    const csvText = iconv.decode(csvBuffer, 'GB18030')

    sendMsg('read', 30, 'processing')
    if (csvText.length < READ_CHUNK_SIZE) {
      const parsed = Papa.parse(csvText, {
        skipEmptyLines: true,
      })
      callback(parsed.data)
    } else {
      // let chunkId = 0
      // const totalChunkNums = Math.ceil(csvText.length / READ_CHUNK_SIZE)
      Papa.parse(csvText, {
        skipEmptyLines: true,
        chunkSize: READ_CHUNK_SIZE,
        // chunk: function (results) {
        //   chunkId += 1
        //   const process = Math.round((0.3 + (chunkId / totalChunkNums) * 0.7) * 100)
        //   sendMsg({
        //     uploadId,
        //     step: 'read',
        //     progress: process,
        //     status: 'processing',
        //     message: '',
        //   })
        // },
        complete: function (results) {
          callback(results.data)
        },
      })
    }
  } catch (error) {
    sendMsg('read', 0, 'proerrorcessing', {}, '读取 CSV 文件失败: ' + error.message)
  }
}

const convertToCSV = (array) => {
  return array.map((row) => row.map((field) => String(field)).join(',')).join('\n')
}

const conver1DArrayToCSV = (array) => {
  return Array.from(array)
    .map((value, index) => `${index + 1},${value}`)
    .join('\n')
}

const saveFile = (data, fileName) => {
  //sendMsg({ uploadId, step: 'writeFile', progress: 0, status: 'processing', message: '' })
  sendMsg('writeFile', 0, 'processing')
  const timestamp = Date.now()
  const tempFileName = `tmp-${timestamp}${fileName}`
  const writeStream = fs.createWriteStream(tempFileName, { encoding: 'utf8' })
  writeStream.write(data)
  writeStream.end(() => {
    sendMsg('writeFile', 100, 'finished')
  })
}

// 统一的上传方法，根据 inputType 来区分是上传文件还是上传字符串
const uploadData = async (
  input, // 文件路径或字符串数据
  inputType, // 输入类型：'file' 或 'data'
  fileName, // 文件名
  serverUrl, // 服务器 URL
  userId, // 用户 ID
  chunkSize = 1024 * 1024, // 数据块大小
  uploadDataType = '',
) => {
  try {
    //sendMsg({ uploadId, step: 'upload', progress: 0, status: 'processing', message: '' })
    sendMsg('upload', 0, 'processing', {}, '', uploadDataType === '' ? undefined : uploadDataType)
    let stored_filepath = ''
    const client = new LinkerClient(serverUrl)

    // 验证输入类型
    if (inputType !== 'file' && inputType !== 'data') {
      throw new Error('Invalid inputType. Must be "file" or "data"')
    }

    // 如果是文件类型但输入不是字符串，或者文件不存在
    if (inputType === 'file' && (typeof input !== 'string' || !fs.existsSync(input))) {
      throw new Error('Invalid file path')
    }

    let totalSize
    let totalSha256
    let chunkCount = 0

    if (inputType === 'file') {
      // 处理文件上传
      const fileStats = fs.statSync(input)
      totalSize = fileStats.size
      totalSha256 = crypto.createHash('sha256').update(fs.readFileSync(input)).digest('hex')
    } else {
      // 处理数据上传
      const stringData = typeof input === 'string' ? input : JSON.stringify(input)
      totalSize = Buffer.byteLength(stringData)
      totalSha256 = crypto.createHash('sha256').update(stringData).digest('hex')
    }

    chunkCount = Math.ceil(totalSize / chunkSize)

    const metadata = new grpc.Metadata()
    metadata.add('method', 'uploadFile')
    metadata.add('source', 'client')
    // 这里上传文件需要获取到文件的URL step-1
    let isSendMsg = false
    const uploadStream = client.uploadFile(metadata, (response, responseType) => {
      if (responseType === 'data') {
        if (response.status === 'Success') {
          stored_filepath = response.stored_filepath
          sendPathToRenderer()
        }
      } else {
        sendMsg('upload', 0, 'error', {}, response)
      }
    })
    const sendPathToRenderer = () => {
      if (stored_filepath && !isSendMsg) {
        sendMsg(
          'upload',
          100,
          'finished',
          {
            stored_filepath: stored_filepath,
          },
          '',
          uploadDataType === '' ? undefined : uploadDataType, // 如果 dataType 为空，不传（使用默认值）
        )
        isSendMsg = true
      }
    }
    let chunkId = 0
    const processChunk = (chunk, isLast) => {
      const start = chunkId * chunkSize
      const uploadProgress = (start / totalSize) * 100
      sendMsg(
        'upload',
        uploadProgress,
        'progressing',
        {},
        '',
        uploadDataType === '' ? undefined : uploadDataType,
      )

      try {
        uploadStream.write({
          file_name: chunkId === 0 ? fileName : '', // 只在第一块发送文件名
          user_id: chunkId === 0 ? userId : '', // 只在第一块发送用户ID
          total_sha256: chunkId === 0 ? totalSha256 : '', // 只在第一块发送哈希
          chunk_id: chunkId,
          content: Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk),
          is_last: isLast, // 标记最后一块
        })
      } catch (error) {
        console.log(error)
      }

      chunkId++
    }
    if (inputType === 'file') {
      // 文件流处理
      const fileStream = fs.createReadStream(input, { highWaterMark: chunkSize })

      fileStream.on('data', (chunk) => {
        processChunk(chunk, chunkId === chunkCount - 1)
      })
      fileStream.on('end', () => {
        uploadStream.end()
        sendPathToRenderer()
        // setTimeout(() => {
        //   sendMsg(
        //     'upload',
        //     100,
        //     'finished',
        //     {
        //       stored_filepath: stored_filepath,
        //     },
        //     '',
        //     uploadDataType === '' ? undefined : uploadDataType, // 如果 dataType 为空，不传（使用默认值）
        //   )
        // }, 500)
      })

      fileStream.on('error', (error) => {
        sendMsg('upload', 0, 'error', {}, error)
        uploadStream.emit('error', error)
      })
    } else {
      // 数据处理
      const stringData = typeof input === 'string' ? input : JSON.stringify(input)
      const stringStream = new Readable({
        read() {
          const start = chunkId * chunkSize
          const end = Math.min(start + chunkSize, stringData.length)
          if (start >= stringData.length) {
            this.push(null) // 流结束
            return
          }

          const chunk = stringData.slice(start, end)
          const isLast = end >= stringData.length
          this.push(chunk)
          processChunk(chunk, isLast)
        },
      })

      stringStream.resume()

      stringStream.on('end', () => {
        uploadStream.end()
        sendPathToRenderer()
        // setTimeout(() => {
        //   sendMsg(
        //     'upload',
        //     100,
        //     'finished',
        //     {
        //       stored_filepath: stored_filepath,
        //     },
        //     '',
        //     uploadDataType === '' ? undefined : uploadDataType, // 如果 dataType 为空，不传（使用默认值）
        //   )
        // }, 500)
      })

      stringStream.on('error', (error) => {
        sendMsg('upload', 0, 'error', {}, error)
        uploadStream.emit('error', error)
      })
    }
    uploadStream.on('error', (error) => {
      sendMsg('upload', 0, 'error', {}, error)
    })
  } catch (error) {
    sendMsg('upload', 0, 'error', {}, error.message || String(error))
  }
}

class LinkerClient {
  constructor(serverUrl, options = {}) {
    this.defaultOptions = {
      'grpc.max_send_message_length': 10 * 1024 * 1024, // 最大发送消息大小 10MB
      'grpc.max_receive_message_length': 10 * 1024 * 1024, // 最大接收消息大小 10MB
    }
    const finalOptions = {
      ...this.defaultOptions,
      ...options,
    }
    let protoPath = new URL('../protos/linker.proto', import.meta.url).pathname
    if (process.platform === 'win32' && protoPath.startsWith('/')) {
      protoPath = protoPath.substring(1)
    }
    const packageDefinition = protoLoader.loadSync(protoPath, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    })
    const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)
    const linkerService = protoDescriptor.linker.LinkerService

    this.grpcClient = new linkerService(serverUrl, grpc.credentials.createInsecure(), finalOptions)
  }

  uploadFile(metadata, callBack) {
    try {
      const stream = this.grpcClient.uploadFile((error, response) => {
        if (error) {
          callBack(error, 'error')
        }
        if (response) {
          if (callBack) {
            callBack(response, 'data')
          }
        }
      }, metadata)

      stream.on('end', () => {
        callBack(null, 'end')
      })

      stream.on('error', (error) => {
        callBack(error, 'error')
      })
      return stream
    } catch (error) {
      callBack(error, null)
    }
  }
}

handleUploadCsvFile()
