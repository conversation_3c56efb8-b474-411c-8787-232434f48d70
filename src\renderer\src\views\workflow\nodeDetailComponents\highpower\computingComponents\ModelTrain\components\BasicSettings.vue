<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="inChannels" class="text-sm font-medium">inChannels</Label>
                <Input
                  id="inChannels"
                  :value="props.modelParams.inChannels"
                  type="number"
                  min="1"
                  placeholder="请输入输入通道数"
                  class="h-10 w-full"
                  @input="updateParam('inChannels', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="channels" class="text-sm font-medium">channels</Label>
                <Input
                  id="channels"
                  :value="props.modelParams.channels"
                  type="number"
                  min="1"
                  placeholder="请输入通道数"
                  class="h-10 w-full"
                  @input="updateParam('channels', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentHeight" class="text-sm font-medium">
                  input_segment_height
                </Label>
                <Input
                  id="inputSegmentHeight"
                  :value="props.modelParams.inputSegmentHeight"
                  type="number"
                  min="1"
                  placeholder="请输入输入段高度"
                  class="h-10 w-full"
                  @input="
                    updateParam('inputSegmentHeight', ($event.target as HTMLInputElement).value)
                  "
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentWidth" class="text-sm font-medium">
                  input_segment_width
                </Label>
                <Input
                  id="inputSegmentWidth"
                  :value="props.modelParams.inputSegmentWidth"
                  type="number"
                  min="1"
                  placeholder="请输入输入段宽度"
                  class="h-10 w-full"
                  @input="
                    updateParam('inputSegmentWidth', ($event.target as HTMLInputElement).value)
                  "
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="alpha" class="text-sm font-medium">alpha</Label>
                <Input
                  id="alpha"
                  :value="props.modelParams.alpha"
                  type="number"
                  step="0.1"
                  placeholder="请输入alpha值"
                  class="h-10 w-full"
                  @input="updateParam('alpha', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="kernelSize" class="text-sm font-medium">kernel_size</Label>
                <Input
                  id="kernelSize"
                  :value="props.modelParams.kernelSize"
                  type="number"
                  min="1"
                  placeholder="请输入核大小"
                  class="h-10 w-full"
                  @input="updateParam('kernelSize', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="trainSupportSize" class="text-sm font-medium">train_support_size</Label>
                <Input
                  id="trainSupportSize"
                  :value="props.modelParams.trainSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入训练支持大小"
                  class="h-10 w-full"
                  @input="
                    updateParam('trainSupportSize', ($event.target as HTMLInputElement).value)
                  "
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="testSupportSize" class="text-sm font-medium">test_support_size</Label>
                <Input
                  id="testSupportSize"
                  :value="props.modelParams.testSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入测试支持大小"
                  class="h-10 w-full"
                  @input="updateParam('testSupportSize', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="actFn" class="text-sm font-medium">act_fn</Label>
                <Select
                  id="actFn"
                  :value="props.modelParams.actFn?.default"
                  class="w-full"
                  @update:value="updateParam('actFn.default', $event)"
                >
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择激活函数" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="choice in props.modelParams.actFn?.choices || []"
                      :key="choice"
                      :value="choice"
                    >
                      {{ choice }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- 布尔值参数 -->
              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">use_fc_for_prediction</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="useFcForPrediction"
                    type="checkbox"
                    :checked="props.modelParams.useFcForPrediction"
                    class="h-4 w-4"
                    @change="
                      updateParam('useFcForPrediction', ($event.target as HTMLInputElement).checked)
                    "
                  />
                  <Label for="useFcForPrediction" class="text-sm">启用FC预测</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">filter_cycles_flag</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="filterCyclesFlag"
                    type="checkbox"
                    :checked="props.modelParams.filterCyclesFlag"
                    class="h-4 w-4"
                    @change="
                      updateParam('filterCyclesFlag', ($event.target as HTMLInputElement).checked)
                    "
                  />
                  <Label for="filterCyclesFlag" class="text-sm">过滤循环标志</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">return_pointwise_predictions</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="returnPointwisePredictions"
                    type="checkbox"
                    :checked="props.modelParams.returnPointwisePredictions"
                    class="h-4 w-4"
                    @change="
                      updateParam(
                        'returnPointwisePredictions',
                        ($event.target as HTMLInputElement).checked,
                      )
                    "
                  />
                  <Label for="returnPointwisePredictions" class="text-sm">返回逐点预测</Label>
                </div>
              </div>

              <!-- 字符串参数 -->
              <div class="flex flex-col space-y-2">
                <Label for="featuresToDrop" class="text-sm font-medium">features_to_drop</Label>
                <Input
                  id="featuresToDrop"
                  :value="props.modelParams.featuresToDrop"
                  placeholder="请输入要丢弃的特征"
                  class="h-10 w-full"
                  @input="updateParam('featuresToDrop', ($event.target as HTMLInputElement).value)"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="cyclesToDropInSegment" class="text-sm font-medium">
                  cycles_to_drop_in_segment
                </Label>
                <Input
                  id="cyclesToDropInSegment"
                  :value="props.modelParams.cyclesToDropInSegment"
                  placeholder="请输入段中要丢弃的循环"
                  class="h-10 w-full"
                  @input="
                    updateParam('cyclesToDropInSegment', ($event.target as HTMLInputElement).value)
                  "
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

const props = defineProps({
  modelParams: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:modelParams'])

// 简化的更新方法
const updateParam = (paramPath: string, value: any) => {
  const newParams = { ...props.modelParams }

  // 处理嵌套属性，如 'actFn.default'
  if (paramPath.includes('.')) {
    const [parent, child] = paramPath.split('.')
    newParams[parent] = { ...newParams[parent], [child]: value }
  } else {
    newParams[paramPath] = value
  }

  emit('update:modelParams', newParams)
}
</script>

<style></style>
