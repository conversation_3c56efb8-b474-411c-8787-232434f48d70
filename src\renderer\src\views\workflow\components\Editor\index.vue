<template>
  <div ref="editorContainerRef" class="workflow-editor w-full h-full">
    <Resizable class="flex flex-row h-[calc(100vh-8vh)]">
      <ResizablePanelGroup direction="horizontal">
        <!-- 工具栏面板 -->
        <Transition name="slide">
          <ResizablePanel v-show="showToolbar" :default-size="10" :min-size="0" :max-size="20">
            <div class="h-full p-2 bg-background">
              <WorkflowToolbar :workflow-title="workflowTitle" />
            </div>
          </ResizablePanel>
        </Transition>

        <!-- 编辑器面板 -->
        <ResizableHandle with-handle />

        <ResizablePanel :default-size="60">
          <div ref="workflowEditorRef" class="workflow h-full">
            <WorkflowEditor :workflow-id="workflowId" @toggle-toolbar="toggleToolbar" />
          </div>
        </ResizablePanel>

        <!-- 节点设置面板 -->
        <ResizableHandle with-handle />
        <Transition name="slide-right">
          <template v-if="nodeNavbarStore.showNodeSettings">
            <ResizablePanel :default-size="30" :min-size="0" :max-size="60">
              <NodeSettingTabs @update="handleNodeUpdate" @save="handleNodeSave" />
            </ResizablePanel>
          </template>
        </Transition>
      </ResizablePanelGroup>
    </Resizable>
  </div>
</template>

<script setup lang="ts">
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@renderer/components/ui/resizable'
import { NodeSettingTabs, WorkflowEditor, WorkflowToolbar } from '@renderer/components/workflow'
import { useFlowsStore, useNodeNavbarStore } from '@renderer/store'
import { ref } from 'vue'

const props = defineProps({
  workflowId: {
    type: String,
    required: true,
  },
  workflowTitle: {
    type: String,
    required: true,
    default: '',
  },
})

const nodeNavbarStore = useNodeNavbarStore()
const flowsStore = useFlowsStore()

const showToolbar = ref(true)
const workflowEditorRef = ref(null)
const editorContainerRef = ref(null)

const toggleToolbar = () => {
  showToolbar.value = !showToolbar.value
}

// 处理节点更新
const handleNodeUpdate = (nodeId, newData) => {
  // 这里可以添加额外的处理逻辑
  console.log('Node updated:', nodeId, newData)
}

// 处理节点保存
const handleNodeSave = (nodeId, data) => {
  // 获取当前工作流的所有元素
  const workflow = flowsStore.getWorkflow(props.workflowId)
  if (!workflow) return

  // 更新节点
  const nodes = workflow.nodes.map((node) => (node.id === nodeId ? data : node))

  // 保存到 store
  flowsStore.saveWorkflow(props.workflowId, {
    nodes,
    edges: workflow.edges || [],
  })
}
</script>

<style lang="scss" scoped>
.workflow-editor {
  @apply w-full h-full;
}

/* 过渡动画 */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform 0.3s ease;
}

.slide-right-enter-from,
.slide-right-leave-to {
  transform: translateX(100%);
}
</style>
