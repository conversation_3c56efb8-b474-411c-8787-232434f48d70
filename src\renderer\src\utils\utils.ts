import type { Updater } from '@tanstack/vue-table'
import type { Ref } from 'vue'
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import dayjs from 'dayjs'

import type {
  SaveFileOptions,
  OpenFileOptions,
  EncryptFileOptions,
  FileOperationResult,
} from '@renderer/config/interface/file'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function valueUpdater<T extends Updater<any>>(updaterOrValue: T, ref: Ref) {
  ref.value = typeof updaterOrValue === 'function' ? updaterOrValue(ref.value) : updaterOrValue
}

/**
 * 将字符串从 snake_case 转换为 camelCase
 * @param str snake_case 格式的字符串
 * @returns camelCase 格式的字符串
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 递归地将对象的所有属性从 snake_case 转换为 camelCase
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertToCamelCase(item))
  }
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const camelKey = toCamelCase(key)
      result[camelKey] = convertToCamelCase(obj[key])
      return result
    }, {} as any)
  }
  return obj
}

/**
 * 检查 electronAPI 是否可用
 * @returns 检查结果
 */
const checkElectronAPI = (): boolean => {
  if (!window.electronAPI) {
    console.error('electronAPI 不可用，请确保在 Electron 环境中运行')
    return false
  }
  return true
}

/**
 * 保存文件
 * @param options 保存文件选项
 * @returns 保存结果
 */
export const saveFile = async (options: SaveFileOptions): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.saveFile(options)
  } catch (error) {
    console.error('保存文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 打开文件对话框
 * @param options 打开文件选项
 * @returns 打开结果
 */
export const openFile = async (options: OpenFileOptions = {}): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.openFile(options)
  } catch (error) {
    console.error('打开文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 读取文件内容
 * @param filePath 文件路径或读取选项
 * @returns 文件内容
 */
export const readFile = async (
  filePath: string | { path: string; encoding?: string; isBinary?: boolean },
): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.readFile(filePath)
  } catch (error) {
    console.error('读取文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 加密并保存文件
 * @param options 加密文件选项
 * @returns 保存结果
 */
export const encryptAndSaveFile = async (
  options: EncryptFileOptions,
): Promise<FileOperationResult> => {
  if (!checkElectronAPI()) {
    return { success: false, error: 'electronAPI 不可用' }
  }

  try {
    return await window.electronAPI.encryptAndSaveFile(options)
  } catch (error) {
    console.error('加密保存文件失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}
export const formatTimestamp = (timestamp?: string | number) => {
  if (!timestamp) return ''
  return dayjs(Number(timestamp) * (String(timestamp).length === 10 ? 1000 : 1)).format(
    'YYYY-MM-DD HH:mm:ss',
  )
}

// 格式化日期
export const formatDate = (date: Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 计算持续时间
export const formatDuration = (startTime: number, endTime: number) => {
  if (!startTime || startTime === 0) return '未开始'
  const start = startTime * 1000
  const end = endTime && endTime !== 0 ? endTime * 1000 : Date.now()
  const durationMs = end - start
  if (durationMs < 0) return '0h 0m 0s'
  const hours = Math.floor(durationMs / (1000 * 60 * 60))
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((durationMs % (1000 * 60)) / 1000)
  return `${hours}h ${minutes}m ${seconds}s`
}
