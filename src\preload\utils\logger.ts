import log from 'electron-log'
import path from 'path'

/**
 * 日志级别类型
 */
export type LogLevel = 'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly'

/**
 * 初始化日志系统
 * @param options 日志配置选项
 */
export function initLogger(
  options: {
    processType?: string
    logLevel?: LogLevel
    consoleLevel?: LogLevel
    maxSize?: number
  } = {},
): typeof log {
  const {
    processType = 'preload',
    logLevel = 'info',
    consoleLevel = 'debug',
    maxSize = 10 * 1024 * 1024, // 10MB
  } = options

  try {
    // 检查 log.transports.file 是否存在
    if (log.transports.file) {
      // 设置日志级别
      log.transports.file.level = logLevel
      log.transports.file.maxSize = maxSize

      // 尝试设置日志文件路径
      try {
        log.transports.file.resolvePath = () => {
          // 获取当前日期，格式为 YYYY-MM-DD
          const now = new Date()
          const year = now.getFullYear()
          const month = String(now.getMonth() + 1).padStart(2, '0')
          const day = String(now.getDate()).padStart(2, '0')
          const dateStr = `${year}-${month}-${day}`

          // 在渲染进程中，我们无法直接访问应用路径，所以使用相对路径
          return path.join('logs', `${processType}-${dateStr}.log`)
        }
      } catch (error) {
        console.error('设置日志路径失败:', error)
      }
    }

    // 设置控制台日志级别
    if (log.transports.console) {
      log.transports.console.level = consoleLevel
    }

    // 记录初始化信息
    log.info(`${processType} 日志系统初始化完成`, {
      time: new Date().toISOString(),
    })
  } catch (error) {
    console.error('日志初始化失败:', error)
  }

  return log
}

// 创建一个简单的日志接口，以防 electron-log 在某些环境中不可用
const safeLogger = {
  error: (...args: any[]) => {
    try {
      log.error(...args)
    } catch (e) {
      console.error(...args)
    }
  },
  warn: (...args: any[]) => {
    try {
      log.warn(...args)
    } catch (e) {
      console.warn(...args)
    }
  },
  info: (...args: any[]) => {
    try {
      log.info(...args)
    } catch (e) {
      console.info(...args)
    }
  },
  debug: (...args: any[]) => {
    try {
      log.debug(...args)
    } catch (e) {
      console.debug(...args)
    }
  },
  verbose: (...args: any[]) => {
    try {
      log.verbose(...args)
    } catch (e) {
      console.log(...args)
    }
  },
  silly: (...args: any[]) => {
    try {
      log.silly(...args)
    } catch (e) {
      console.log(...args)
    }
  },
  transports: {
    file: {
      getFile: () => ({
        path: 'logs/preload.log',
      }),
    },
  },
}

// 导出预配置的日志实例
export const logger = initLogger()

// 默认导出安全的日志接口
export default safeLogger
