<template>
  <div
    v-if="nodeNavbarStore.showNodeSettings"
    ref="panelRef"
    class="node-settings-panel h-full container-root"
  >
    <div class="flex flex-col h-full">
      <!-- 标签页标题 -->
      <div class="tabs-header border-b relative bg-background">
        <!-- 左侧切换按钮 -->
        <button
          class="tab-nav-button left-1 z-20"
          :class="{ disabled: isFirstTabVisible }"
          :disabled="isFirstTabVisible"
          @click="scrollTabs('left')"
        >
          <Icon icon="flowbite:angle-left-outline" :width="16" :height="16" />
        </button>

        <div ref="tabsContainerRef" class="tabs-scroll-container mx-7">
          <Tabs v-model="activeTab" class="w-full">
            <TabsList class="w-full overflow-x-auto scrollbar tabs-list">
              <div ref="tabsListRef" class="tabs-inner-container">
                <!-- 修改这里：为每个标签单独创建ContextMenu -->
                <template v-for="tab in nodeNavbarStore.tabs" :key="tab.id">
                  <ContextMenu>
                    <ContextMenuTrigger class="inline-block">
                      <TabsTrigger
                        ref="tabRefs"
                        :value="tab.id"
                        class="relative pr-8 bg-muted hover:bg-muted/80 mx-1 whitespace-nowrap text-foreground ring-1 ring-border data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:ring-1 data-[state=active]:ring-primary/30"
                        @contextmenu="() => handleContextMenu(tab.id)"
                      >
                        {{ tab.label }}
                        <button
                          class="absolute right-1 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-destructive/20 text-muted-foreground hover:text-destructive"
                          @click.stop="handleCloseTab(tab.id)"
                        >
                          <Icon icon="flowbite:close-outline" :width="14" :height="14" />
                        </button>
                      </TabsTrigger>
                    </ContextMenuTrigger>
                    <ContextMenuContent>
                      <ContextMenuItem @click="handleCloseTab(tab.id)">
                        <Icon icon="flowbite:close-outline" :width="16" :height="16" class="mr-2" />
                        关闭标签
                      </ContextMenuItem>
                      <ContextMenuItem @click="handleCloseOtherTabs(tab.id)">
                        <Icon
                          icon="flowbite:close-circle-outline"
                          :width="16"
                          :height="16"
                          class="mr-2"
                        />
                        关闭其他标签
                      </ContextMenuItem>
                      <ContextMenuItem @click="handleCloseRightTabs(tab.id)">
                        <Icon
                          icon="flowbite:arrow-right-outline"
                          :width="16"
                          :height="16"
                          class="mr-2"
                        />
                        关闭右侧标签
                      </ContextMenuItem>
                      <ContextMenuItem @click="handleCloseLeftTabs(tab.id)">
                        <Icon
                          icon="flowbite:arrow-left-outline"
                          :width="16"
                          :height="16"
                          class="mr-2"
                        />
                        关闭左侧标签
                      </ContextMenuItem>
                      <ContextMenuSeparator />
                      <ContextMenuItem @click="handleCloseAllTabs">
                        <Icon
                          icon="flowbite:close-circle-outline"
                          :width="16"
                          :height="16"
                          class="mr-2"
                        />
                        关闭所有标签
                      </ContextMenuItem>
                    </ContextMenuContent>
                  </ContextMenu>
                </template>
              </div>
            </TabsList>
          </Tabs>
        </div>

        <!-- 右侧切换按钮 -->
        <button
          class="tab-nav-button right-1 z-20"
          :class="{ disabled: isLastTabVisible }"
          :disabled="isLastTabVisible"
          @click="scrollTabs('right')"
        >
          <Icon
            icon="flowbite:angle-right-outline"
            :width="16"
            :height="16"
            class="text-foreground"
          />
        </button>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content flex-1 h-full p-4 bg-background">
        <Tabs v-model="activeTab" class="w-full h-full">
          <TabsContent
            v-for="tab in nodeNavbarStore.tabs"
            :key="tab.id"
            :value="tab.id"
            class="h-full container-content"
          >
            <component
              :is="getNodeDetailComponent(tab.nodeData)"
              v-if="getNodeDetailComponent(tab.nodeData)"
              :node-data="tab.nodeData"
              class="w-full h-full overflow-y-auto scrollbar"
              @update:node-data="(newData) => handleNodeDataUpdate(tab.nodeId, newData)"
              @save="(data) => handleSave(tab.nodeId, data)"
              @close="() => handleCloseTab(tab.id)"
              @custom-action="handleCustomAction"
            />

            <div v-else class="p-4 text-center text-gray-500">暂无可用的编辑组件</div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { themeColorMap } from '@renderer/config/constants'
import { useFlowsStore, useNodeNavbarStore, useSettingsStore } from '@renderer/store'
import nodeDetailComponents from '@renderer/views/workflow/nodeDetailComponents'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

const emit = defineEmits(['update', 'save', 'custom-action', 'resize'])

const nodeNavbarStore = useNodeNavbarStore()
const flowsStore = useFlowsStore()
const settingsStore = useSettingsStore()

const panelRef = ref<HTMLElement | null>(null)
const tabsContainerRef = ref<HTMLElement | null>(null)
const tabsListRef = ref<HTMLElement | null>(null)
const tabRefs = ref<HTMLElement[]>([])
const contextMenuTabId = ref<string>('')
// 当前主题 - 使用设置存储
const currentTheme = computed(() => settingsStore.theme || 'city-light')

// 当前活动标签
const activeTab = computed({
  get: () => nodeNavbarStore.activeTabId,
  set: (value) => {
    if (value) {
      nodeNavbarStore.setActiveTab(value)
    }
  },
})

// 判断是否第一个标签可见
const isFirstTabVisible = computed(() => {
  // 如果标签数量小于等于1，禁用左右按钮
  if (nodeNavbarStore.tabs.length <= 1) return true

  // 如果当前是第一个标签，禁用左按钮
  const activeIndex = nodeNavbarStore.tabs.findIndex(
    (tab) => tab.id === nodeNavbarStore.activeTabId,
  )
  return activeIndex === 0
})

// 判断是否最后一个标签可见
const isLastTabVisible = computed(() => {
  // 如果标签数量小于等于1，禁用左右按钮
  if (nodeNavbarStore.tabs.length <= 1) return true

  // 如果当前是最后一个标签，禁用右按钮
  const activeIndex = nodeNavbarStore.tabs.findIndex(
    (tab) => tab.id === nodeNavbarStore.activeTabId,
  )
  return activeIndex === nodeNavbarStore.tabs.length - 1 || activeIndex === -1
})

// 根据节点类型获取对应的详情组件
const getNodeDetailComponent = (nodeData: any) => {
  // console.log('根据节点类型获取对应的详情组件', nodeData)
  if (!nodeData?.data?.type) return null

  const nodeType = nodeData.data.type
  const nodeCategory = nodeData.data.category || ''

  // 尝试按优先级查找组件
  // 1. 直接按类型查找 (例如: Electrode)
  if (nodeDetailComponents[nodeType]) {
    return nodeDetailComponents[nodeType]
  }

  // 2. 按类别-类型格式查找 (例如: batterySimulationDetails-Electrode)
  if (nodeCategory && nodeDetailComponents[`${nodeCategory}-${nodeType}`]) {
    return nodeDetailComponents[`${nodeCategory}-${nodeType}`]
  }

  // 3. 按文件夹-类型格式查找
  for (const key in nodeDetailComponents) {
    if (key.endsWith(`-${nodeType}`)) {
      return nodeDetailComponents[key]
    }
  }

  console.warn('未找到对应的组件:', nodeType, nodeCategory)
  return null
}

// 处理关闭标签
const handleCloseTab = (tabId: string) => {
  nodeNavbarStore.removeTab(tabId)
}
// 关闭所有标签
const handleCloseAllTabs = () => {
  nodeNavbarStore.closeAllTabs()
}
// 处理右键菜单
const handleContextMenu = (tabId: string) => {
  contextMenuTabId.value = tabId
}
// 关闭右侧标签
const handleCloseRightTabs = (tabId: string) => {
  const currentIndex = nodeNavbarStore.tabs.findIndex((tab) => tab.id === tabId)
  if (currentIndex === -1) return

  // 关闭标签
  nodeNavbarStore.closeRightTabs(tabId)
}

// 关闭左侧标签
const handleCloseLeftTabs = (tabId: string) => {
  const currentIndex = nodeNavbarStore.tabs.findIndex((tab) => tab.id === tabId)
  if (currentIndex === -1) return

  // 关闭标签
  nodeNavbarStore.closeLeftTabs(tabId)
}

// 关闭其他标签
const handleCloseOtherTabs = (tabId: string) => {
  const currentIndex = nodeNavbarStore.tabs.findIndex((tab) => tab.id === tabId)
  if (currentIndex === -1) return

  // 关闭其他标签
  nodeNavbarStore.closeRightTabs(tabId)
  nodeNavbarStore.closeLeftTabs(tabId)
}
// 处理节点数据更新
const handleNodeDataUpdate = (nodeId: string, newData: any) => {
  // console.log('Node data updated:', nodeId, newData)
  nodeNavbarStore.updateNodeData(nodeId, newData)

  // 保存节点参数到 flows.ts
  if (newData && newData.data) {
    const workflowId = newData.data.workflowId
    if (workflowId && newData.id && newData.data.params) {
      // 保存参数到 store
      flowsStore.saveNodeParams(workflowId, newData.id, newData.data.params)
    }
  }

  // 触发外部更新事件
  emit('update', nodeId, newData)
}

// 处理保存
const handleSave = (nodeId: string, data: any) => {
  // console.log('Save node data:', nodeId, data)
  // 触发外部保存事件
  emit('save', nodeId, data)
}

// 处理自定义操作
const handleCustomAction = (action: string, data?: any) => {
  // console.log('Custom action:', action, data)
  emit('custom-action', action, data)
}

// 滚动标签
const scrollTabs = (direction: 'left' | 'right') => {
  // console.log('scrollTabs', direction)

  // 获取当前活动标签的索引
  const activeIndex = nodeNavbarStore.tabs.findIndex(
    (tab) => tab.id === nodeNavbarStore.activeTabId,
  )
  if (activeIndex === -1) return

  let newIndex
  if (direction === 'left') {
    // 向左滚动，选择前一个标签
    newIndex = Math.max(0, activeIndex - 1)
  } else {
    // 向右滚动，选择后一个标签
    newIndex = Math.min(nodeNavbarStore.tabs.length - 1, activeIndex + 1)
  }

  // 设置新的活动标签
  if (newIndex !== activeIndex && nodeNavbarStore.tabs[newIndex]) {
    nodeNavbarStore.setActiveTab(nodeNavbarStore.tabs[newIndex].id)
  }
}

// 背景色缓存优化
const backgroundColorCache = new Map()

// 颜色前缀查找表
const colorPrefixMap = {
  materialDesign: {
    Basic: 'material-basic',
    Compute: 'material-compute',
    Data: 'material-data',
    default: 'material-basic',
  },
  batterySimulation: {
    Basic: 'battery-basic',
    Compute: 'battery-compute',
    Data: 'battery-data',
    default: 'battery-basic',
  },
  default: 'material-basic',
}

// 简化的哈希函数
const simpleHash = (str: string) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = (hash << 5) - hash + str.charCodeAt(i)
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

// 根据节点类型和当前主题获取背景色
const getNodeBackgroundColor = (node: any) => {
  const nodeId = node.id
  const nodeType = node.data.type
  const nodeCategory = node.data.category || ''

  // 检查缓存
  const cacheKey = `${nodeId}-${nodeType}-${nodeCategory}-${currentTheme.value}`
  if (backgroundColorCache.has(cacheKey)) {
    return backgroundColorCache.get(cacheKey)
  }

  // 使用查找表确定颜色键前缀
  let colorKeyPrefix = 'material-basic'

  if (nodeCategory) {
    const categoryMap = colorPrefixMap[nodeCategory]
    if (categoryMap) {
      colorKeyPrefix = categoryMap[nodeType] || categoryMap.default
    }
  }

  // 获取当前主题下该类型的颜色数组
  const themeColors = themeColorMap[currentTheme.value] || {}
  const colorArray = themeColors[colorKeyPrefix] || []

  if (colorArray.length === 0) {
    return '#f2f2f2' // 默认颜色
  }

  // 优化哈希计算
  const colorIndex = simpleHash(nodeId) % colorArray.length
  const color = colorArray[colorIndex] || '#f2f2f2'

  // 存入缓存
  backgroundColorCache.set(cacheKey, color)
  return color
}

// 监听标签变化，自动滚动到活动标签
watch(
  () => nodeNavbarStore.activeTabId,
  (newId) => {
    if (!newId || !tabsContainerRef.value) return

    nextTick(() => {
      const activeTabElement = tabRefs.value.find((el: any) => el.value === newId)
      if (activeTabElement) {
        activeTabElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' })
      }
    })
  },
)

// 组件挂载后初始化
onMounted(() => {
  // 初始化逻辑
})
onBeforeUnmount(() => {
  // 清理逻辑
})
</script>

<style lang="scss" scoped>
.node-settings-panel {
  @apply bg-background border-l border-gray-200 w-full h-full overflow-hidden;
  container-type: inline-size;
  container-name: settings-panel;
}

.tabs-header {
  @apply flex items-center;
  height: 40px;
  position: relative;
  z-index: 10;
}

.tabs-scroll-container {
  @apply flex-1 overflow-hidden;
  position: relative;
}

.tabs-list {
  @apply flex items-center;
  justify-content: flex-start; /* 确保标签左对齐 */
}

.tabs-inner-container {
  @apply flex items-center;
  min-width: 100%;
}

.tab-nav-button {
  @apply flex items-center justify-center p-1 rounded hover:bg-muted bg-background shadow-sm;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  /* background-color: white; */
  /* box-shadow: 0 0 3px rgba(0, 0, 0, 0.1); */

  &.disabled {
    @apply opacity-50 cursor-not-allowed;
    &:hover {
      @apply bg-transparent;
    }
  }
}

.tab-content {
  height: calc(100% - 40px);
  position: relative;
  z-index: 5;
  /* overflow: auto; */
}
.container-content {
  container-type: inline-size;
  container-name: content-area;
}
/* 使用 vw 单位结合容器查询实现更一致的响应式布局 */
@container content-area (max-width: calc(30vw)) {
  /* 窄屏幕 - 单列布局 */
  :deep(.grid) {
    @apply grid-cols-1;
  }
}

@container content-area (min-width: calc(30vw + 1px)) and (max-width: calc(45vw)) {
  /* 中等宽度 - 双列布局 */
  :deep(.grid) {
    @apply grid-cols-2;
  }
}

@container content-area (min-width: calc(45vw + 1px)) and (max-width: calc(60vw)) {
  /* 较宽宽度 - 三列布局 */
  :deep(.grid) {
    @apply grid-cols-3;
  }
}

@container content-area (min-width: calc(60vw + 1px)) {
  /* 宽屏幕 - 四列布局 */
  :deep(.grid) {
    @apply grid-cols-4;
  }
}
.resize-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 19;
  cursor: ew-resize;
}
</style>
