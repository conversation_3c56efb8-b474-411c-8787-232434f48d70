const path = require('path')
const fs = require('fs').promises
import logger from '../utils/logger'
const grpc = require('@grpc/grpc-js')
const protoLoader = require('@grpc/proto-loader')
// 封装思路，获取存根
interface FsReaddir {
  // fs.readdir 的返回值类型,用到的属性
  name: string // 文件名称
  path: string // 文件路径
  isDirectory: () => boolean // 是否是目录
}
// 注册服务
class GRPCClient {
  // public async getStub() {
  //   const dirPath = path.join(__dirname, 'protos')
  //   // const protoFiles = await this.getAllProtoFiles(dirPath)
  // }
  private async getAllProtoFiles(dirPath: string) {
    let results: string[] = []
    try {
      const items: FsReaddir[] = await fs.readdir(dirPath, { withFileTypes: true })
      for (let i = 0; i < items.length; i++) {
        const item: FsReaddir = items[i]
        const fullPath: string = path.join(dirPath, item.name)
        if (item.isDirectory()) {
          results = results.concat(await this.getAllProtoFiles(fullPath))
        } else {
          results.push(fullPath)
        }
      }
    } catch (error) {
      logger.info(`Error reading directory ${dirPath}:${error}`)
    }
    return results
  }
  /**
   * 加载并解析.proto文件
   * @param {string[]} protoFiles .proto文件路径数组
   * @returns {Promise<Object>} gRPC包定义
   */
  private async loadProtoDefinitions(protoFiles) {
    if (protoFiles.length === 0) {
      throw new Error('No .proto files provided')
    }

    const includeDirs = [
      ...new Set(protoFiles.map((file) => path.dirname(file))), // 获取所有不重复的目录
    ]
    const options = {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs,
    }

    try {
      const packageDefinition = await protoLoader.load(protoFiles, options)
      return grpc.loadPackageDefinition(packageDefinition)
    } catch (error) {
      logger.info(`Error loading proto definitions:${error}`)
      throw error
    }
  }
  /**
   * 注册所有找到的gRPC服务
   * @param {Object} packageDefinition 从proto加载的包定义
   * @param {string} serverAddress 服务端地址，如'localhost:50051'
   */
  private services: any = {}
  private methodsInservices: any = new Map()
  private DEFAULT_OPTIONS = {
    'grpc.max_send_message_length': 10 * 1024 * 1024, // 最大发送消息大小 10MB
    'grpc.max_receive_message_length': 10 * 1024 * 1024, // 最大接收消息大小 10MB
  }
  private registerAllServices(packageDefinition: any, serverAddress: any, options?: any) {
    // 清除现有服务
    this.services = {}
    this.methodsInservices.clear()

    // 遍历包定义中的所有服务
    for (const [packageName, packageContent] of Object.entries(packageDefinition)) {
      if (typeof packageContent !== 'object') continue
      for (const [serviceName, ServiceClient] of Object.entries(packageContent as any)) {
        // 检查是否为有效的服务客户端
        const serviceClient: any = ServiceClient
        if (ServiceClient && serviceClient.service && typeof serviceClient.service === 'object') {
          const fullServiceName = `${packageName}.${serviceName}`
          let opts = this.DEFAULT_OPTIONS
          if (options) {
            opts = {
              ...this.DEFAULT_OPTIONS,
              ...options,
            }
          }
          this.services[fullServiceName] = new serviceClient(
            serverAddress,
            grpc.credentials.createInsecure(),
            opts,
          )
          for (const [methodName, method] of Object.entries(serviceClient.service)) {
            const md: any = method
            // 查看不同服务中的方法是否有重复
            let key: string = methodName
            if (this.methodsInservices.has(methodName)) {
              key = `${fullServiceName}.${methodName}`
            }
            this.methodsInservices.set(methodName, {
              fullServiceName,
              requestStream: md.requestStream,
              responseStream: md.responseStream,
            })
          }
        }
      }
    }
  }
  /**
   * 获取指定服务的客户端
   * @param {string} serviceName 服务名称(格式: packageName.ServiceName)
   * @returns {Object} gRPC客户端实例
   */
  public getServiceClient(serviceName) {
    const client = this.services[serviceName]
    if (!client) {
      throw new Error(`Service ${serviceName} not found`)
    }
    return client
  }
  public getServiceMethods(methodName) {
    const service = this.methodsInservices.get(methodName)
    if (!service) {
      throw new Error(`Method ${methodName} not found`)
    }
    return service
  }
  // private packageDefinitions: any = {} // 存储加载的proto定义
  /**
   * 初始化gRPC服务管理器
   * @param {string} protoDir .proto文件所在目录
   * @param {string} serverAddress gRPC服务端地址
   */
  public async initialize(serverAddress: string, options?: Record<string, any>) {
    const dirPath = path.join(__dirname, 'protos') // proto文件所在目录
    try {
      // 1. 获取所有.proto文件
      const protoFiles = await this.getAllProtoFiles(dirPath)

      if (protoFiles.length === 0) {
        throw new Error('No .proto files found in the specified directory')
      }

      // 2. 加载proto定义
      const packageDefinition = await this.loadProtoDefinitions(protoFiles)
      // this.packageDefinitions = packageDefinition
      // 3. 注册所有服务
      this.registerAllServices(packageDefinition, serverAddress, options)

      return { success: true, services: Object.keys(this.services) }
    } catch (error: any) {
      console.error('Initialization failed:', error)
      return { success: false, error: error.message }
    }
  }
}
export default GRPCClient
