<template>
  <BaseChartPanel
    :title="title"
    :has-data="isFinished"
    :is-disabled="isParamsExtract"
    @reset="resetInput"
  >
    <FileDropUpload
      v-show="!isFinished && !isLoding"
      ref="cycleDataChartfileDropUploadRef"
      :accept-types="['.csv']"
      :max-size="1024"
      :progress-control="true"
      @file-selected="(file) => $emit('file-selected', file)"
      @error="(error) => $emit('upload-error', error)"
      @progress="(progress) => $emit('upload-progress', progress)"
      @upload-complete="$emit('upload-complete')"
    />

    <CycleSetting
      v-if="isFinished"
      :cycle-params="cycleParams"
      :is-disabled="isParamsExtract"
      @update:cycle-params="updateCycleParams"
    />

    <div v-if="isFinished" v-chart-resize-multi="chartInstances" class="grid grid-cols-1 gap-6">
      <div v-for="type in chartTypes" :key="type.key" class="border border-gray-200 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-800 mb-2">{{ type.label }}数据</h3>
        <div class="w-full aspect-[4/3] relative">
          <div
            :ref="
              (el) => {
                if (el) chartRefs[type.key] = el
              }
            "
            class="w-full h-full absolute inset-0"
          ></div>
        </div>
      </div>
    </div>
  </BaseChartPanel>
</template>

<script setup>
import { FileDropUpload } from '@renderer/components'
import { IndexedDBHelper } from '@renderer/database/IndexedDBHelper'
import * as echarts from 'echarts'
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { CycleSetting } from '.'
import BaseChartPanel from './BaseChartPanel.vue'

const props = defineProps({
  title: String,
  isResetting: Boolean,
  isParamsExtract: Boolean,
  cycleParams: Object,
  uploadStoredId: String,
})

const emit = defineEmits([
  'reset',
  'file-selected',
  'upload-error',
  'upload-progress',
  'upload-complete',
  'chart-ready',
  'update:cycle-params',
])

const chartData = ref([])
const chartRefs = ref({ current: null, voltage: null, capacity: null })
const chartInstances = ref({})
const isActive = ref(true)
const isFinished = ref(false)
const isLoding = ref(false)
const dbHelper = new IndexedDBHelper('uploadDatabase', 1)
const safeDispose = (chart) => {
  if (!chart) return null
  try {
    if (typeof chart.dispose === 'function') {
      chart.dispose()
    }
  } catch (e) {
    console.error('清理图表实例失败:', e)
  }
  return null
}
const cycleDataChartfileDropUploadRef = ref(null)
const chartTypes = [
  {
    key: 'current',
    label: '电流',
    title: '电流-时间曲线',
    yName: '电流 (A)',
    color: '#409eff',
    seriesName: '电流',
    tooltipUnit: 'A',
  },
  {
    key: 'voltage',
    label: '电压',
    title: '电压-时间曲线',
    yName: '电压 (V)',
    color: '#67c23a',
    seriesName: '电压',
    tooltipUnit: 'V',
  },
  {
    key: 'capacity',
    label: '容量',
    title: '容量-时间曲线',
    yName: '容量 (Ah)',
    color: '#f56c6c',
    seriesName: '容量',
    tooltipUnit: 'Ah',
  },
]

const updateCycleParams = (newParams) => {
  emit('update:cycle-params', newParams)
}

const cleanupCharts = () => {
  Object.keys(chartInstances.value).forEach((key) => {
    if (chartInstances.value[key]) {
      chartInstances.value[key].dispose()
      chartInstances.value[key] = null
    }
  })
}

function processCycleCapacityData(data) {
  const cycleMap = new Map()
  data.forEach(({ cycle, capacity }) => {
    if (!isNaN(cycle) && !isNaN(capacity)) {
      if (!cycleMap.has(cycle) || capacity > cycleMap.get(cycle)) {
        cycleMap.set(cycle, capacity)
      }
    }
  })
  const entries = Array.from(cycleMap.entries()).sort((a, b) => a[0] - b[0])
  return { cycles: entries.map((e) => e[0]), maxCapacities: entries.map((e) => e[1]) }
}

function getChartOption(type, yAxisData, seriesData) {
  if (type.key === 'capacity') {
    const capacityOption = {
      title: { text: '容量-循环次数曲线', left: 'center', top: 5 },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: yAxisData },
      yAxis: { type: 'value' },
      series: [
        {
          name: '最大容量',
          type: 'line',
          data: seriesData,
          smooth: true,
          symbol: 'none',
          lineStyle: { color: type.color },
        },
      ],
      animation: false,
    }
    // 当startCycleValue有值且不为0时，添加标记线
    if (props.cycleParams.startCycleValue && props.cycleParams.startCycleValue !== 0) {
      capacityOption.series[0].markLine = {
        silent: true,
        symbol: ['none', 'none'],
        label: {
          formatter: `起始圈: ${props.cycleParams.startCycleValue}`,
          position: 'middle',
          backgroundColor: 'rgba(255, 69, 0, 0.8)',
          padding: [4, 8],
          borderRadius: 4,
          color: '#fff',
          fontSize: 12,
          distance: 10,
          show: true,
          rotate: 0, // 设置为0度，使标签水平显示
        },
        lineStyle: {
          type: 'solid',
          color: '#757575',
          width: 2,
        },
        data: [
          {
            xAxis: props.cycleParams.startCycleValue,
            name: '起始圈',
          },
        ],
      }
    }
    return capacityOption
  }
  return {
    title: {
      text: type.title,
      left: 'center',
      top: 5,
      textStyle: { fontSize: 14 },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        const param = params[0]
        return `${param.seriesName} <br/>时间: ${param.name} s <br/>${type.seriesName}: ${param.value} ${type.tooltipUnit}`
      },
      axisPointer: {
        type: 'cross',
        label: { backgroundColor: '#6a7985' },
      },
    },
    grid: { top: 30, right: 50, bottom: 60, left: 60 },
    xAxis: {
      type: 'category',
      name: '时间 (HH:MM:SS)',
      data: yAxisData,
      nameLocation: 'center',
      nameGap: 30,
      nameTextStyle: { fontSize: 14, fontWeight: 'bold' },
    },
    yAxis: { type: 'value', name: type.yName, scale: true },
    animation: false, // 关闭全局动画
    series: [
      {
        name: type.seriesName,
        type: 'line',
        data: seriesData,
        smooth: true,
        symbol: 'none',
        animation: false,
        lineStyle: { color: type.color },
        large: true, // 开启大数据模式
        largeThreshold: 20000, // 可选，默认2000，超过该数量自动启用
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        filterMode: 'empty',
        zoomLock: false, // 允许缩放
        zoomOnMouseWheel: true, // 鼠标滚轮缩放
        moveOnMouseMove: true, // 鼠标拖动
        moveOnMouseWheel: false, // 禁用鼠标滚轮平移
        preventDefaultMouseMove: true, // 阻止默认行为
        onZoom: (event) => {
          handleZoom(event) // 缩放时触发的函数
        },
      },
      {
        type: 'slider',
        show: false,
        start: 0,
        end: 100,
        height: 12, // 调整滑块高度
        bottom: 2, // 调整滑块位置
        // backgroundColor: '#f5f5f5', // 背景色
        // fillerColor: '#409eff', // 选中区域颜色
        // borderColor: '#ddd', // 边框颜色
        // handleSize: 8, // 调整手柄大小
        // handleStyle: {
        //   color: '#409eff', // 手柄颜色
        //   borderColor: '#fff', // 手柄边框颜色
        //   borderWidth: 1, // 手柄边框宽度
        // },
        // textStyle: {
        //   color: '#666', // 文字颜色
        //   fontSize: 10, // 文字大小
        // },
      },
    ],
  }
}

const setData = (newData = {}) => {
  if (!isActive.value) return
  if (newData && Object.keys(newData).length !== 0) {
    chartData.value = newData
  }
  isFinished.value = true
  nextTick(() => {
    const tt0 = performance.now()
    cleanupCharts()
    if (props.isResetting) return
    const timeData = downsampleData(chartData.value.time).map((timeInSeconds) =>
      formatTimeDiff(timeInSeconds),
    )
    chartTypes.forEach((type) => {
      try {
        const el = chartRefs.value[type.key]
        if (!el || el.offsetWidth === 0 || el.offsetHeight === 0) return
        const chart = echarts.init(el)
        chartInstances.value[type.key] = chart
        const seriesData = downsampleData(chartData.value[type.key])

        //const seriesData = chartData.value.map((item) => item[type.key])
        //console.log('seriesDat11a', seriesData)
        let option
        if (type.key === 'capacity') {
          const cycleData = Array.from({ length: seriesData.length }, (_, i) => i + 1)
          option = getChartOption(type, cycleData, seriesData)
        } else {
          option = getChartOption(type, timeData, seriesData)
        }
        chart.setOption(option)
      } catch (error) {
        console.log('chart error')
        console.log(error)
      }
    })
    emit('chart-ready', chartInstances.value)
  })
}

const downsampleData = (data, start = 0, end = 100) => {
  const targetDataPoints = 50000
  const totalDataLength = data.length
  if (totalDataLength < targetDataPoints) {
    return Array.from(data)
  }
  // 根据缩放的比例计算出当前视口显示的数据点数量
  const visibleDataCount = Math.ceil(((end - start) / 100) * totalDataLength)

  // 计算当前的采样间隔（目标数据点数量 / 视口内数据点数量）
  const samplingInterval = Math.max(Math.floor(visibleDataCount / targetDataPoints), 1)
  // 获取当前视口内的数据
  const visibleDataStartIndex = Math.floor((start / 100) * totalDataLength)
  const visibleDataEndIndex = Math.floor((end / 100) * totalDataLength)
  const actualStart = Math.min(visibleDataStartIndex, totalDataLength - 1)
  const actualEnd = Math.min(visibleDataEndIndex, totalDataLength)

  const visibleData = data.slice(actualStart, actualEnd) // 截取当前视口的数据
  const result = []
  for (let i = 0; i < visibleData.length; i += samplingInterval) {
    result.push(visibleData[i])
  }

  if (result[result.length - 1] !== visibleData[visibleData.length - 1]) {
    result.push(visibleData[visibleData.length - 1])
  }

  return result
}

//下采样函数：每隔 `samplingInterval` 个数据点取一个数据点
const handleZoom = (event) => {
  const { start, end } = event // 获取 dataZoom 的缩放范围

  let currentChartKey

  Object.keys(chartInstances).forEach((key) => {
    const chart = chartInstances[key]
    if (chart.isDisposed()) return
    if (chart.getDom() === event.target) {
      currentChartKey = key
    }
  })

  const downsampledData = downsampleData(chartData.value[currentChartKey], start)

  chart.setOption({
    series: [
      {
        data: downsampledData,
      },
    ],
  })
}

const formatTimeDiff = (milliseconds) => {
  // 计算总秒数
  const totalSeconds = Math.floor(milliseconds / 1000)
  // 计算小时、分钟和秒
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60
  // 格式化为两位数
  const pad = (num) => num.toString().padStart(2, '0')

  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`
}

const resetInput = () => {
  isFinished.value = false
  cycleDataChartfileDropUploadRef.value.resetUploading()
  progressStatus.read.value = 0
  progressStatus.processData.value = 0
  progressStatus.upload.value = 0
  if (props.uploadStoredId !== '' || props.uploadStoredId !== undefined) {
    {
      dbHelper
        .open()
        .then(() => {
          dbHelper.deleteData('UploadData', props.uploadStoredId)
        })
        .catch((error) => {
          console.error('Error opening database:', error)
        })
    }
  }
  setTimeout(() => {
    emit('reset')
  }, 100)
}

onMounted(() => {
  isActive.value = true
  isLoding.value = true
  if (props.uploadStoredId !== '') {
    console.log(props.uploadStoredId)
    setTimeout(() => {
      if (isActive.value) {
        dbHelper
          .open()
          .then(() => {
            dbHelper
              .getData('UploadData', props.uploadStoredId)
              .then((storedData) => {
                if (storedData) {
                  setData(storedData.data)
                  isLoding.value = false
                } else {
                  isLoding.value = false
                }
              })
              .catch((error) => {
                isLoding.value = false
              })
          })
          .catch((error) => {
            isLoding.value = false
          })

        //initChart()
      }
    }, 100)
  } else {
    isLoding.value = false
  }
})

watch(
  () => props.cycleParams,
  () => {
    if (isActive.value && !props.isResetting) {
      setData()
    }
  },
  { deep: true },
)
onUnmounted(() => {
  isActive.value = false
  cleanupCharts()
})

const progressStatus = {
  read: ref(0),
  processData: ref(0),
  upload: ref(0),
}

const updateProgress = (step, progress) => {
  progressStatus[step].value = progress
  const uploadProgress =
    Math.round(
      (5 +
        progressStatus.read.value * 0.2 +
        progressStatus.processData.value * 0.5 +
        progressStatus.upload.value * 0.2) *
        100,
    ) / 100
  if (isActive.value) {
    cycleDataChartfileDropUploadRef.value.updateProgress(uploadProgress)
  }
}

defineExpose({ setData, updateProgress })
</script>

<style scoped></style>
