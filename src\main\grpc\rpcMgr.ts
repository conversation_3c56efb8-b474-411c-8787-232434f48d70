const grpc = require('@grpc/grpc-js')
const { getProtocol } = require('./protocol')
import logger from '../utils/logger'

export class RpcMgr {
  private linkerUrl: string
  private user_id: string
  private token: string
  private grpcClientOptions: Record<string, any>
  private linker: any
  private connected: boolean = false
  constructor(linkerUrl, user_id, token) {
    this.linkerUrl = linkerUrl
    this.user_id = user_id
    this.token = token
    this.grpcClientOptions = {
      'grpc.max_send_message_length': 100 * 1024 * 1024,
      'grpc.max_receive_message_length': 100 * 1024 * 1024,
    }
    this.connect(linkerUrl)
  }
  //更新认证信息
  updateAuthInfo(userId: string, token: string) {
    if (userId && token) {
      this.user_id = userId
      this.token = token
      logger.info(`认证信息已更新: userId=${userId}`)
      return true
    }
    return false
  }

  call(apiName, params, callBack, allowRetry = true, callType = 'default') {
    try {
      if (!this.linker) {
        logger.error('linker 未连接，尝试重新连接')
        this.connect(this.linkerUrl)
        if (!this.linker) {
          logger.error('linker 重连失败')
          callBack(new Error('linker 未连接或连接失败'), null)
          return
        }
      }
      if (typeof this.linker[apiName] !== 'function') {
        logger.error(`接口 ${apiName} 不存在，请检查 linker.proto 文件`)
        callBack(new Error(`接口 ${apiName} 不存在`), null)
        return
      }
      params.user_id = this.user_id
      params.token = this.token
      const metadata = new grpc.Metadata()
      metadata.add('method', apiName)
      metadata.add('source', 'client')
      logger.info(`调用 ${apiName}，参数:`, params)
      if (callType == 'default') {
        this.linker[apiName](params, metadata, (error, response) =>
          this.preCallBack(error, response, apiName, params, callBack, allowRetry),
        )
      } else if (callType == 'serverStream') {
        const service = this.linker[apiName](params, metadata, (error, response) =>
          this.preCallBack(error, response, apiName, params, callBack, allowRetry),
        )
        service.on('data', function (response) {
          //logger.info(response)
          callBack(response, 'data')
        })
        service.on('end', function () {
          //logger.info('end')
          callBack(undefined, 'end')
        })
        service.on('error', function (error) {
          //logger.error(error)
          callBack(error, 'error')
        })
      }
    } catch (error) {
      logger.error(`调用 ${apiName} 时发生异常:`, error)
      callBack(error, null)
    }
  }

  connect(linkerUrl = '') {
    try {
      linkerUrl = linkerUrl || this.linkerUrl
      logger.info(`尝试连接 gRPC 服务器: ${linkerUrl}`)
      if (!linkerUrl || linkerUrl.trim() === '') {
        logger.error(`无效的服务器地址: "${linkerUrl}"`)
        return
      }
      const protocol = getProtocol('linker')
      if (!protocol || !protocol.LinkerService) {
        logger.error('无法获取 LinkerService，可能由于 .proto 文件加载失败')
        return
      }
      const serviceName = this.getServiceNameByProtocol('linker')
      if (!protocol[serviceName]) {
        logger.error(`协议中不存在服务 ${serviceName}`)
        return
      }
      this.linker = new protocol[serviceName](
        linkerUrl,
        grpc.credentials.createInsecure(),
        this.grpcClientOptions,
      )
      // 检查连接状态
      const channel = this.linker.getChannel()
      const state = channel.getConnectivityState(true)
      logger.info(
        `连接状态: ${state} (0=IDLE, 1=CONNECTING, 2=READY, 3=TRANSIENT_FAILURE, 4=SHUTDOWN)`,
      )
      if (state === grpc.connectivityState.READY) {
        logger.info(`成功连接到 ${linkerUrl}`)
        this.connected = true
      } else {
        logger.warn(`连接未就绪，状态: ${state}`)
        this.connected = false
      }
    } catch (error) {
      logger.error(`连接 ${linkerUrl} 失败:`, error)
      this.connected = false
    }
  }

  // 检查连接状态
  isConnected() {
    if (!this.linker) return false
    try {
      const channel = this.linker.getChannel()
      const state = channel.getConnectivityState(true)
      this.connected = state === grpc.connectivityState.READY
      return this.connected
    } catch (error) {
      logger.error('检查连接状态失败:', error)
      this.connected = false
      return false
    }
  }

  // 获取当前连接的URL
  getLinkerUrl() {
    return this.linkerUrl
  }

  // 提交同步任务方法
  default(serviceName, serverId = '', is_save = false, params, callBack) {
    const defaultParams = { task_id: '', server_id: '', key_type_pairs: {}, key_value_pairs: {} }
    params = Object.assign(defaultParams, params)
    params.service_name = serviceName
    params.server_id = serverId
    params.is_save = is_save
    this.call('defaultService', params, callBack)
  }

  // 提交任务方法
  submit(serviceName, serverId = '', is_save = false, params, callBack) {
    const defaultParams = { task_id: '', server_id: '', key_type_pairs: {}, key_value_pairs: {} }
    params = Object.assign(defaultParams, params)
    params.service_name = serviceName
    params.server_id = serverId
    params.is_save = is_save
    this.call('submitService', params, callBack)
  }

  // 调用Agent
  agent(
    serviceName,
    serverId = '',
    sessionId,
    modelType,
    isStreamResponse = true,
    messages,
    callBack,
    abortSignal,
  ) {
    try {
      if (!this.linker) {
        logger.error('linker 未连接，尝试重新连接')
        this.connect(this.linkerUrl)
        if (!this.linker) {
          logger.error('linker 重连失败')
          callBack(new Error('linker 未连接或连接失败'), 'error')
          return
        }
      }

      const params = {} as any
      params.service_name = serviceName
      params.server_id = serverId
      params.session_id = sessionId
      params.model_type = modelType
      params.is_stream_response = isStreamResponse
      params.messages = messages
      params.user_id = this.user_id
      params.token = this.token

      // 添加对AbortSignal的支持
      const metadata = new grpc.Metadata()
      metadata.add('method', 'agentService')
      metadata.add('source', 'client')

      logger.info(`调用 agentService，参数:`, {
        service_name: serviceName,
        server_id: serverId,
        session_id: sessionId,
        model_type: modelType,
        is_stream_response: isStreamResponse,
      })

      // 使用serverStream方式调用
      const service = this.linker.agentService(params, metadata)

      // 如果提供了AbortSignal，添加中断处理
      if (abortSignal) {
        abortSignal.addEventListener('abort', () => {
          logger.info(`中断会话 ${sessionId} 的流式响应`)
          service.cancel()
          callBack(undefined, 'end')
        })
      }

      service.on('data', function (response) {
        callBack(response, 'data')
      })

      service.on('end', function () {
        callBack(undefined, 'end')
      })

      service.on('error', function (error) {
        // 检查是否是客户端取消导致的错误
        if (error && error.code === 1 && error.details === 'Cancelled on client') {
          logger.info(`会话 ${sessionId} 的流式响应已被客户端取消`)
          callBack({ message: '用户取消了请求' }, 'end')
        } else {
          // 其他真正的错误
          logger.error(`agentService 流错误:`, error)
          callBack(error, 'error')
        }
      })

      return service
    } catch (error) {
      logger.error(`Agent调用失败: ${error}`)
      callBack(error, 'error')
    }
  }

  //创建客户端流失链接
  setClient(userName, loginIp = '', password, expiredTime = 0, callBack) {
    const params = {} as any
    params.user_name = userName
    params.login_ip = loginIp
    params.password = password
    params.expired_time = expiredTime

    try {
      if (!this.linker) {
        logger.error('linker 未连接，尝试重新连接')
        this.connect(this.linkerUrl)
        if (!this.linker) {
          callBack(new Error('linker 未连接或连接失败'), 'error')
          return
        }
      }

      logger.info(`建立 setClientChannel 长连接，参数:`, params)
      // 添加认证信息
      params.user_id = this.user_id
      params.token = this.token

      // 创建流式连接
      const stream = this.linker.setClientChannel(params)

      // 处理数据流
      stream.on('data', (response) => {
        // 处理心跳消息
        if (response.message === 'Ping') {
          logger.debug('收到心跳消息')
        } else {
          logger.info(`收到 setClientChannel 数据:`, response)
        }

        // 将数据传递给回调函数，第一个参数是数据，第二个参数是类型
        callBack(response, 'data')
      })

      // 处理结束
      stream.on('end', () => {
        logger.info('setClientChannel 连接已结束')
        callBack(null, 'end')
      })

      // 处理错误
      stream.on('error', (error) => {
        logger.error('setClientChannel 连接错误:', error)
        callBack(error, 'error')
      })

      // 返回流对象，以便外部可以控制
      return stream
    } catch (error) {
      logger.error(`setClientChannel 调用异常:`, error)
      callBack(error, 'error')
      return null
    }
  }

  // 目前流式的功能全部封装在这个文件中，所以客户端流也放在这里处理 Client-side streaming RPC：客户端流式 RPC
  // 注册文件上传链接，并传入初始化参数，目前只用于uploadFile接口，后续可以进行扩展
  // 这里只负责链接客户端流式传输
  clientSideStream(callBack?: any) {
    try {
      if (!this.linker) {
        logger.error('linker 未连接，尝试重新连接')
        this.connect(this.linkerUrl)
        if (!this.linker) {
          callBack(new Error('linker 未连接或连接失败'), 'error')
          return
        }
      }
      // 添加认证信息
      // params.user_id = this.user_id
      // 创建流式连接，需要传递出回调函数
      const metadata = new grpc.Metadata()
      metadata.add('method', 'uploadFile')
      metadata.add('source', 'client')
      const stream = this.linker.uploadFile(metadata, (error, response) => {
        if (error) {
          callBack(error, 'error')
        }
        if (response) {
          if (callBack) {
            callBack(response, 'data')
          }
        }
        logger.info('返回结果:', response)
      })
      // 处理结束
      stream.on('end', () => {
        logger.info('clientSideStream 连接已结束')
        callBack(null, 'end')
      })

      // 处理错误
      stream.on('error', (error) => {
        logger.error('clientSideStream 连接错误:', error)
        callBack(error, 'error')
      })

      // 返回流对象，以便外部可以控制
      return stream
    } catch (error) {
      logger.error(`clientSideStream 调用异常:`, error)
      callBack(error, 'error')
      return null
    }
  }
  // 建议关于grpc的初始化都在这里做
  // 获取当前用户id
  getUsers() {
    return this.user_id
  }

  getServiceNameByProtocol(protocolName) {
    const serviceMap = {
      linker: 'LinkerService',
      matt: 'MattService',
      db: 'DbService',
      common: 'CommonService',
    }
    return (
      serviceMap[protocolName] ||
      `${protocolName.charAt(0).toUpperCase() + protocolName.slice(1)}Service`
    )
  }

  preCallBack(error, response, apiName, params, callBack, allowRetry) {
    if (error && error.toString().startsWith('Error: 14 UNAVAILABLE')) {
      logger.error(`gRPC 调用 ${apiName} 失败: 服务不可用`, error)
      this.connect(this.linkerUrl)
      if (allowRetry) {
        this.call(apiName, params, callBack, false)
      } else {
        callBack(error, null)
      }
    } else {
      callBack(error, response)
    }
  }
}
