import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { parse, stringify } from 'zipson'

export const useFlowsStore = defineStore(
  'flows',
  () => {
    // state
    // 存储所有工作流的数据，格式: { workflowId: { nodes: [], edges: [] } }
    const workflows = ref({})
    const currentWorkflowId = ref(null)

    // getters
    const currentWorkflow = computed(() => workflows.value[currentWorkflowId.value] || null)

    // actions
    function setCurrentWorkflow(workflowId) {
      currentWorkflowId.value = workflowId
    }
    // 监听节点链接
    let nodeLinkCallback = null
    function listenNodeLink(callback) {
      nodeLinkCallback = callback
    }
    function getListeners() {
      return {
        nodeLink: nodeLinkCallback,
      }
    }

    function saveWorkflow(workflowId, data) {
      workflows.value = {
        ...workflows.value,
        [workflowId]: {
          nodes: data.nodes.map((node: any) => ({
            ...node,
            data: {
              ...node.data,
              label: node.data.label || '未命名节点',
              description: node.data.description || '',
              // backgroundColor: node.data.backgroundColor || '#ffffff',
              icon: node.data.icon || {
                type: 'icon',
                value: 'flowbite:draw-square-outline',
              },
              params: node.data.params || {},
            },
          })),
          edges: data.edges.map((edge: any) => ({
            ...edge,
            type: edge.type || 'smoothstep',
            animated: edge.animated !== undefined ? edge.animated : false, // 线条默认设置为 false
          })),
        },
      }
    }

    function getWorkflow(workflowId) {
      return workflows.value[workflowId] || { nodes: [], edges: [] }
    }

    function createWorkflow(workflowId) {
      if (!workflows.value[workflowId]) {
        workflows.value[workflowId] = {
          nodes: [],
          edges: [],
        }
      }
    }

    function deleteWorkflow(workflowId: string) {
      if (workflows.value[workflowId]) {
        // 删除工作流数据
        delete workflows.value[workflowId]
      }
    }

    function saveNodeParams(workflowId, nodeId, params) {
      const workflow = workflows.value[workflowId]
      if (workflow && workflow.nodes) {
        const nodeIndex = workflow.nodes.findIndex((node) => node.id === nodeId)
        if (nodeIndex !== -1) {
          // 确保 data 和 params 对象存在
          if (!workflow.nodes[nodeIndex].data) {
            workflow.nodes[nodeIndex].data = {}
          }
          if (!workflow.nodes[nodeIndex].data.params) {
            workflow.nodes[nodeIndex].data.params = {}
          }

          // 更新参数
          workflow.nodes[nodeIndex].data.params = {
            ...workflow.nodes[nodeIndex].data.params,
            ...params,
          }
        }
      }
    }

    function getNodeParams(workflowId, nodeId) {
      // 如果单独存储中没有，再从 workflows 中获取
      const workflow = workflows.value[workflowId]
      if (workflow && workflow.nodes) {
        const node = workflow.nodes.find((node) => node.id === nodeId)
        if (node && node.data && node.data.params) {
          return node.data.params
        }
      }
      return {}
    }

    function deleteNodeParams(workflowId, nodeId) {
      const workflow = workflows.value[workflowId]
      if (workflow && workflow.nodes) {
        const nodeIndex = workflow.nodes.findIndex((node) => node.id === nodeId)
        if (nodeIndex !== -1 && workflow.nodes[nodeIndex].data) {
          workflow.nodes[nodeIndex].data.params = {}
        }
      }
    }

    return {
      // state
      workflows,
      currentWorkflowId,
      // getters
      currentWorkflow,
      // actions
      setCurrentWorkflow,
      saveWorkflow,
      createWorkflow,
      deleteWorkflow,
      getWorkflow,
      saveNodeParams,
      getNodeParams,
      deleteNodeParams,
      listenNodeLink,
      getListeners,
    }
  },
  {
    persist: {
      // key: 'flow-store',
      storage: localStorage,
      serializer: {
        deserialize: parse,
        serialize: stringify,
      },
      pick: ['workflows'],
    },
  },
)
