<template>
  <div class="relative">
    <Card class="p-2">
      <CardHeader>
        <div class="flex flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0">
          <CardTitle class="text-xl sm:text-2xl font-bold">机器学习模型训练</CardTitle>
          <!-- 操作按钮 -->
          <div class="flex flex-wrap gap-1">
            <Button
              size="xs"
              class="flex items-center relative"
              :disabled="state.isProcessing"
              @click="handleAction('start', '开始')"
            >
              <LucideIcon v-if="!state.isProcessing" name="Check" class="w-4 h-4 mr-1" />
              <span v-if="state.isProcessing" class="animate-spin mr-1">
                <LucideIcon name="RefreshCw" class="h-4 w-4" />
              </span>
              <span class="whitespace-nowrap">开始训练</span>
              <span
                v-if="totalSumCpd > 0"
                class="absolute -top-3 -right-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5"
              >
                {{ totalSumCpd }}
              </span>
            </Button>
            <Button
              size="xs"
              class="dark:text-muted-foreground dark:bg-muted-foreground"
              @click="handleAction('export', {})"
            >
              <LucideIcon name="FileUp" class="w-4 h-4 mr-1" />
              <span class="hidden sm:inline">导出结果</span>
              <span class="sm:hidden">导出</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <!-- 进度条显示 -->
        <div
          v-if="state.isProcessing"
          class="mb-4 space-y-2 bg-blue-50 p-3 rounded-lg border border-blue-200"
        >
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center">
              <span class="animate-spin mr-2">
                <LucideIcon name="RefreshCw" class="h-5 w-5 text-blue-500" />
              </span>
              <span class="font-medium text-blue-700">
                <span class="flex items-center justify-center">
                  模型训练中
                  <span class="inline-block w-3 overflow-hidden animate-ellipsis">...</span>
                </span>
              </span>
            </div>
            <span class="text-blue-600">
              处理中 {{ state.taskProgress && state.taskProgress.toFixed(2) }}%
            </span>
          </div>
          <Progress v-model="state.taskProgress" class="w-full h-2" />
          <div class="flex items-center justify-between cursor-pointer">
            <p class="text-xs text-blue-600 mr-2">请耐心等待，任务完成后将自动更新</p>
          </div>
        </div>

        <!-- 基本参数配置 -->
        <BasicSettings :model-params="state.modelParams" @update:model-params="updateModelParams" />
      </CardContent>
    </Card>

    <!-- 提交任务对话框 -->
    <!-- <SubmitTaskDialog
      v-model:is-open="showSubmitDialog"
      service-name="parameterIdentification"
      @submit="handleSubmit"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { useDebounceFn, useThrottleFn } from '@vueuse/core'

import { SubmitTaskDialog, LucideIcon } from '@renderer/components'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useFlowsStore, useTaskStore, useWorkflowStore } from '@renderer/store'
import {
  getNodeParams,
  saveNodeParams,
  updateNodeData as updateNodeDataUtil,
} from '@renderer/utils/nodeUtils'
import { inputParse } from '@renderer/utils/rpcParser'
import { encryptAndSaveFile } from '@renderer/utils/utils'

import { BasicSettings } from './components'

// 组件属性
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

// 服务和状态管理
const taskStore = useTaskStore()
const flowsStore = useFlowsStore()
const taskService = createTaskService()

const state = reactive({
  isProcessing: false, //任务启动加载状态
  taskProgress: 0, //任务进度
  //基本参数配置
  modelParams: {
    inChannels: '',
    channels: '',
    inputSegmentHeight: '',
    inputSegmentWidth: '',
    alpha: '',
    kernelSize: '',
    trainSupportSize: '',
    testSupportSize: '',
    actFn: {
      default: '',
      choices: [],
    },
    useFcForPrediction: false,
    filterCyclesFlag: false,
    featuresToDrop: '',
    cyclesToDropInSegment: '',
    returnPointwisePredictions: false,
  },
})

//===========================计算属性===========================
const totalSumCpd = computed(() => 0) //模型集总和
const trainModelConfig = computed(() => {
  if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
    const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
    return params?.TrainModelConfig || {}
  }
  return {}
})
console.log('==============trainModelConfig===============', trainModelConfig.value)

//===========================方法===========================
/**
 * 从 trainModelConfig 中提取 model_params 并更新 state.modelParams
 */
const updateModelParamsFromConfig = () => {
  const config = trainModelConfig.value
  console.log('updateModelParamsFromConfig', config)

  let modelParams: any = null

  // 尝试从不同的数据结构中获取 model_params
  if (config && config.modelResult) {
    try {
      // 解析 modelResult 中的 JSON 字符串
      const modelData = JSON.parse(config.modelResult)
      console.log('解析后的 modelData:', modelData)
      modelParams = modelData.model_params
    } catch (e) {
      console.error('解析 modelResult 失败:', e)
    }
  } else if (config && config.model_params) {
    // 直接从 config 中获取
    modelParams = config.model_params
  }

  if (modelParams) {
    console.log('找到 modelParams:', modelParams)

    // 直接赋值，保持原始数据类型和值
    state['modelParams']['inChannels'] = modelParams.in_channels
    state['modelParams']['channels'] = modelParams.channels
    state['modelParams']['inputSegmentHeight'] = modelParams.input_segment_height
    state['modelParams']['inputSegmentWidth'] = modelParams.input_segment_width
    state['modelParams']['alpha'] = modelParams.alpha
    state['modelParams']['kernelSize'] = modelParams.kernel_size
    state['modelParams']['trainSupportSize'] = modelParams.train_support_size
    state['modelParams']['testSupportSize'] = modelParams.test_support_size

    // 处理 act_fn 参数
    if (modelParams.act_fn) {
      state['modelParams']['actFn']['default'] = modelParams.act_fn.default
      state['modelParams']['actFn']['choices'] = modelParams.act_fn.choices
    }

    // 处理布尔值参数
    state['modelParams']['useFcForPrediction'] = modelParams.use_fc_for_prediction
    state['modelParams']['filterCyclesFlag'] = modelParams.filter_cycles_flag
    state['modelParams']['returnPointwisePredictions'] = modelParams.return_pointwise_predictions

    // 处理字符串参数
    state['modelParams']['featuresToDrop'] = modelParams.features_to_drop
    state['modelParams']['cyclesToDropInSegment'] = modelParams.cycles_to_drop_in_segment

    console.log('已更新 modelParams:', state.modelParams)
  } else {
    console.log('没有找到 model_params 配置')
  }
}

/**
 * 更新模型参数
 */
const updateModelParams = (newParams: any) => {
  Object.assign(state.modelParams, newParams)
}

/**
 * 开始训练
 */
const startTrainingFn = (item: any) => {
  console.log('startTraining', item)
}

const handleExportFn = () => {
  console.log('handleExportFn')
}

const actions = {
  start: useDebounceFn(startTrainingFn, 1000),
  export: useDebounceFn(handleExportFn, 1000),
}

type ActionName = keyof typeof actions
const handleAction = <T extends ActionName>(name: T, ...args: Parameters<(typeof actions)[T]>) => {
  const targetFunction = actions[name]

  if (typeof targetFunction === 'function') {
    // @ts-ignore - 在某些严格配置下可能需要，但通常泛型已解决问题
    targetFunction(...args)
  } else {
    console.warn(`警告：尝试调用未定义的方法: "${name}"`)
  }
}
//===========================监听===========================
// 监听 trainModelConfig 变化，自动更新 modelParams
watch(
  trainModelConfig,
  () => {
    updateModelParamsFromConfig()
  },
  { immediate: true, deep: true },
)

//===========================生命周期===========================
onMounted(() => {
  updateModelParamsFromConfig()
})
</script>

<style scoped></style>
