<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="inChannels" class="text-sm font-medium">inChannels</Label>
                <Input
                  id="inChannels"
                  v-model="modelParams.inChannels"
                  type="number"
                  min="1"
                  placeholder="请输入输入通道数"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="channels" class="text-sm font-medium">channels</Label>
                <Input
                  id="channels"
                  v-model="modelParams.channels"
                  type="number"
                  min="1"
                  placeholder="请输入通道数"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentHeight" class="text-sm font-medium">
                  input_segment_height
                </Label>
                <Input
                  id="inputSegmentHeight"
                  v-model="modelParams.inputSegmentHeight"
                  type="number"
                  min="1"
                  placeholder="请输入输入段高度"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentWidth" class="text-sm font-medium">
                  input_segment_width
                </Label>
                <Input
                  id="inputSegmentWidth"
                  v-model="modelParams.inputSegmentWidth"
                  type="number"
                  min="1"
                  placeholder="请输入输入段宽度"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="alpha" class="text-sm font-medium">alpha</Label>
                <Input
                  id="alpha"
                  v-model="modelParams.alpha"
                  type="number"
                  step="0.1"
                  placeholder="请输入alpha值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="kernelSize" class="text-sm font-medium">kernel_size</Label>
                <Input
                  id="kernelSize"
                  v-model="modelParams.kernelSize"
                  type="number"
                  min="1"
                  placeholder="请输入核大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="trainSupportSize" class="text-sm font-medium">train_support_size</Label>
                <Input
                  id="trainSupportSize"
                  v-model="modelParams.trainSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入训练支持大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="testSupportSize" class="text-sm font-medium">test_support_size</Label>
                <Input
                  id="testSupportSize"
                  v-model="modelParams.testSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入测试支持大小"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="actFn" class="text-sm font-medium">act_fn</Label>
                <Select id="actFn" v-model="modelParams.actFn.default" class="w-full">
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择激活函数" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="choice in modelParams.actFn?.choices || []"
                      :key="choice"
                      :value="choice"
                    >
                      {{ choice }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <!-- 布尔值参数 -->
              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">use_fc_for_prediction</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="useFcForPrediction"
                    v-model="modelParams.useFcForPrediction"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="useFcForPrediction" class="text-sm">启用FC预测</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">filter_cycles_flag</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="filterCyclesFlag"
                    v-model="modelParams.filterCyclesFlag"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="filterCyclesFlag" class="text-sm">过滤循环标志</Label>
                </div>
              </div>

              <div class="flex flex-col space-y-2">
                <Label class="text-sm font-medium">return_pointwise_predictions</Label>
                <div class="flex items-center space-x-2">
                  <input
                    id="returnPointwisePredictions"
                    v-model="modelParams.returnPointwisePredictions"
                    type="checkbox"
                    class="h-4 w-4"
                  />
                  <Label for="returnPointwisePredictions" class="text-sm">返回逐点预测</Label>
                </div>
              </div>

              <!-- 字符串参数 -->
              <div class="flex flex-col space-y-2">
                <Label for="featuresToDrop" class="text-sm font-medium">features_to_drop</Label>
                <Input
                  id="featuresToDrop"
                  v-model="modelParams.featuresToDrop"
                  placeholder="请输入要丢弃的特征"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="cyclesToDropInSegment" class="text-sm font-medium">
                  cycles_to_drop_in_segment
                </Label>
                <Input
                  id="cyclesToDropInSegment"
                  v-model="modelParams.cyclesToDropInSegment"
                  placeholder="请输入段中要丢弃的循环"
                  class="h-10 w-full"
                />
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

const modelParams = defineModel('modelParams', { type: Object, required: true })
</script>

<style></style>
