<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="inChannels" class="text-sm font-medium">inChannels</Label>
                <Input
                  id="inChannels"
                  v-model="modelParams.inChannels"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="channels" class="text-sm font-medium">channels</Label>
                <Input
                  id="channels"
                  v-model="modelParams.channels"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentHeight" class="text-sm font-medium">
                  input_segment_height
                </Label>
                <Input
                  id="inputSegmentHeight"
                  v-model="modelParams.inputSegmentHeight"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentWidth" class="text-sm font-medium">
                  input_segment_width
                </Label>
                <Input
                  id="inputSegmentWidth"
                  v-model="modelParams.inputSegmentWidth"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="alpha" class="text-sm font-medium">alpha</Label>
                <Input
                  id="alpha"
                  v-model="modelParams.alpha"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="kernelSize" class="text-sm font-medium">kernel_size</Label>
                <Input
                  id="kernelSize"
                  v-model="modelParams.kernelSize"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="trainSupportSize" class="text-sm font-medium">train_support_size</Label>
                <Input
                  id="trainSupportSize"
                  v-model="modelParams.trainSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="testSupportSize" class="text-sm font-medium">test_support_size</Label>
                <Input
                  id="testSupportSize"
                  v-model="modelParams.testSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="testSupportSize" class="text-sm font-medium">test_support_size</Label>
                <Input
                  id="testSupportSize"
                  v-model="modelParams.testSupportSize"
                  type="number"
                  min="1"
                  placeholder="请输入充电功率值"
                  class="h-10 w-full"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="actFn" class="text-sm font-medium">act_fn</Label>
                <Select id="actFn" class="w-full">
                  <SelectTrigger class="h-10">
                    <SelectValue placeholder="请选择运行工况类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="actFns in modelParams.actFn.options"
                      :key="actFns.value"
                      :value="actFns.value"
                    >
                      {{ actFns.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { LucideIcon } from '@renderer/components'

const props = defineProps({
  modelParams: {
    type: Object,
    required: true,
  },
})
</script>

<style></style>
